#ifndef LUNA_PARSER_H
#define LUNA_PARSER_H

#include "luna.h"
#include "lexer.h"
#include "ast.h"

// Parser precedence levels
typedef enum {
    PREC_NONE,
    PREC_ASSIGNMENT,    // =
    PREC_CONDITIONAL,   // ?:
    PREC_OR,           // ||
    PREC_AND,          // &&
    PREC_EQUALITY,     // == !=
    PREC_COMPARISON,   // < > <= >=
    PREC_TERM,         // + -
    PREC_FACTOR,       // * / %
    PREC_UNARY,        // ! -
    PREC_CALL,         // . () []
    PREC_PRIMARY
} LunaPrecedence;

// Parser state
typedef struct {
    LunaLexer* lexer;
    LunaToken current;
    LunaToken previous;
    bool had_error;
    bool panic_mode;
    LunaError error;
} LunaParser;

// Parse rule for Pratt parser
typedef LunaASTNode* (*LunaParseFn)(LunaParser* parser, bool can_assign);

typedef struct {
    LunaParseFn prefix;
    LunaParseFn infix;
    LunaPrecedence precedence;
} LunaParseRule;

// Parser functions
void luna_init_parser(<PERSON><PERSON><PERSON><PERSON>* parser, Luna<PERSON>exer* lexer);
LunaASTNode* luna_parse(LunaParser* parser);
LunaASTNode* luna_parse_expression(LunaParser* parser);
LunaASTNode* luna_parse_statement(LunaParser* parser);
LunaASTNode* luna_parse_declaration(LunaParser* parser);

// Token management
void luna_advance_parser(LunaParser* parser);
bool luna_check(LunaParser* parser, LunaTokenType type);
bool luna_match_parser(LunaParser* parser, LunaTokenType type);
void luna_consume(LunaParser* parser, LunaTokenType type, const char* message);

// Error handling
void luna_error_at(LunaParser* parser, LunaToken* token, const char* message);
void luna_error_at_current(LunaParser* parser, const char* message);
void luna_error_at_previous(LunaParser* parser, const char* message);
void luna_synchronize(LunaParser* parser);

// Expression parsing
LunaASTNode* luna_parse_precedence(LunaParser* parser, LunaPrecedence precedence);
LunaASTNode* luna_parse_primary(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_unary(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_binary(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_call(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_dot(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_index(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_conditional(LunaParser* parser, bool can_assign);
LunaASTNode* luna_parse_assignment(LunaParser* parser, bool can_assign);

// Statement parsing
LunaASTNode* luna_parse_expression_statement(LunaParser* parser);
LunaASTNode* luna_parse_block_statement(LunaParser* parser);
LunaASTNode* luna_parse_if_statement(LunaParser* parser);
LunaASTNode* luna_parse_while_statement(LunaParser* parser);
LunaASTNode* luna_parse_for_statement(LunaParser* parser);
LunaASTNode* luna_parse_switch_statement(LunaParser* parser);
LunaASTNode* luna_parse_return_statement(LunaParser* parser);
LunaASTNode* luna_parse_break_statement(LunaParser* parser);
LunaASTNode* luna_parse_continue_statement(LunaParser* parser);
LunaASTNode* luna_parse_try_statement(LunaParser* parser);
LunaASTNode* luna_parse_throw_statement(LunaParser* parser);

// Declaration parsing
LunaASTNode* luna_parse_var_declaration(LunaParser* parser);
LunaASTNode* luna_parse_function_declaration(LunaParser* parser);
LunaASTNode* luna_parse_class_declaration(LunaParser* parser);

// Utility functions
LunaParseRule* luna_get_rule(LunaTokenType type);
bool luna_is_assignment_target(LunaASTNode* node);

#endif // LUNA_PARSER_H
