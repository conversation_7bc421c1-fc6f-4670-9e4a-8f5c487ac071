#ifndef LUNA_VALUE_H
#define LUNA_VALUE_H

#include "luna.h"

// String object
struct LunaString {
    LunaObject obj;
    int length;
    char* chars;
    uint32_t hash;
};

// Function object
typedef struct {
    LunaObject obj;
    int arity;
    int upvalue_count;
    struct LunaChunk* chunk;
    LunaString* name;
} LunaFunction;

// Native function type
typedef LunaValue (*LunaNativeFn)(int arg_count, LunaValue* args);

// Native function object
typedef struct {
    LunaObject obj;
    LunaNativeFn function;
    LunaString* name;
} LunaNativeFunction;

// Upvalue object
typedef struct LunaUpvalue {
    LunaObject obj;
    LunaValue* location;
    LunaValue closed;
    struct LunaUpvalue* next;
} LunaUpvalue;

// Closure object
typedef struct {
    LunaObject obj;
    LunaFunction* function;
    LunaUpvalue** upvalues;
    int upvalue_count;
} LunaClosure;

// Class object
struct LunaClass {
    LunaObject obj;
    LunaString* name;
    struct LunaTable* methods;
};

// Instance object
struct LunaInstance {
    LunaObject obj;
    LunaClass* klass;
    struct LunaTable* fields;
};

// Bound method object
typedef struct {
    LunaObject obj;
    LunaValue receiver;
    LunaClosure* method;
} LunaBoundMethod;

// Array object
struct LunaArray {
    LunaObject obj;
    int count;
    int capacity;
    LunaValue* elements;
};

// Table entry
typedef struct {
    LunaString* key;
    LunaValue value;
} LunaEntry;

// Hash table object
struct LunaTable {
    LunaObject obj;
    int count;
    int capacity;
    LunaEntry* entries;
};

// Dynamic array for values
typedef struct {
    int capacity;
    int count;
    LunaValue* values;
} LunaValueArray;

// Function prototypes for value operations
LunaString* luna_copy_string(const char* chars, int length);
LunaString* luna_take_string(char* chars, int length);
LunaFunction* luna_new_function(void);
LunaNativeFunction* luna_new_native_function(LunaNativeFn function, LunaString* name);
LunaUpvalue* luna_new_upvalue(LunaValue* slot);
LunaClosure* luna_new_closure(LunaFunction* function);
LunaClass* luna_new_class(LunaString* name);
LunaInstance* luna_new_instance(LunaClass* klass);
LunaBoundMethod* luna_new_bound_method(LunaValue receiver, LunaClosure* method);
LunaArray* luna_new_array(void);
LunaTable* luna_new_table(void);

// Value array operations
void luna_init_value_array(LunaValueArray* array);
void luna_write_value_array(LunaValueArray* array, LunaValue value);
void luna_free_value_array(LunaValueArray* array);

// Array operations
void luna_array_push(LunaArray* array, LunaValue value);
LunaValue luna_array_pop(LunaArray* array);
LunaValue luna_array_get(LunaArray* array, int index);
void luna_array_set(LunaArray* array, int index, LunaValue value);

// Table operations
bool luna_table_get(LunaTable* table, LunaString* key, LunaValue* value);
bool luna_table_set(LunaTable* table, LunaString* key, LunaValue value);
bool luna_table_delete(LunaTable* table, LunaString* key);
void luna_table_add_all(LunaTable* from, LunaTable* to);
LunaString* luna_table_find_string(LunaTable* table, const char* chars, int length, uint32_t hash);

// String operations
uint32_t luna_hash_string(const char* key, int length);
void luna_print_object(LunaValue value);

// Object type checking macros
#define LUNA_OBJ_TYPE(value)        (LUNA_AS_OBJECT(value)->type)

#define LUNA_IS_STRING(value)       luna_is_obj_type(value, LUNA_OBJ_STRING)
#define LUNA_IS_FUNCTION(value)     luna_is_obj_type(value, LUNA_OBJ_FUNCTION)
#define LUNA_IS_NATIVE(value)       luna_is_obj_type(value, LUNA_OBJ_NATIVE_FUNCTION)
#define LUNA_IS_CLOSURE(value)      luna_is_obj_type(value, LUNA_OBJ_CLOSURE)
#define LUNA_IS_CLASS(value)        luna_is_obj_type(value, LUNA_OBJ_CLASS)
#define LUNA_IS_INSTANCE(value)     luna_is_obj_type(value, LUNA_OBJ_INSTANCE)
#define LUNA_IS_BOUND_METHOD(value) luna_is_obj_type(value, LUNA_OBJ_BOUND_METHOD)
#define LUNA_IS_ARRAY(value)        luna_is_obj_type(value, LUNA_OBJ_ARRAY)
#define LUNA_IS_TABLE(value)        luna_is_obj_type(value, LUNA_OBJ_TABLE)

#define LUNA_AS_STRING(value)       ((LunaString*)LUNA_AS_OBJECT(value))
#define LUNA_AS_CSTRING(value)      (((LunaString*)LUNA_AS_OBJECT(value))->chars)
#define LUNA_AS_FUNCTION(value)     ((LunaFunction*)LUNA_AS_OBJECT(value))
#define LUNA_AS_NATIVE(value)       (((LunaNativeFunction*)LUNA_AS_OBJECT(value))->function)
#define LUNA_AS_CLOSURE(value)      ((LunaClosure*)LUNA_AS_OBJECT(value))
#define LUNA_AS_CLASS(value)        ((LunaClass*)LUNA_AS_OBJECT(value))
#define LUNA_AS_INSTANCE(value)     ((LunaInstance*)LUNA_AS_OBJECT(value))
#define LUNA_AS_BOUND_METHOD(value) ((LunaBoundMethod*)LUNA_AS_OBJECT(value))
#define LUNA_AS_ARRAY(value)        ((LunaArray*)LUNA_AS_OBJECT(value))
#define LUNA_AS_TABLE(value)        ((LunaTable*)LUNA_AS_OBJECT(value))

static inline bool luna_is_obj_type(LunaValue value, LunaObjectType type) {
    return LUNA_IS_OBJECT(value) && LUNA_AS_OBJECT(value)->type == type;
}

#endif // LUNA_VALUE_H
