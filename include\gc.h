#ifndef LUNA_GC_H
#define LUNA_GC_H

#include "luna.h"
#include "vm.h"

// Garbage collection functions
void luna_collect_garbage(LunaVM* vm);
void luna_mark_object(LunaObject* object);
void luna_mark_value(LunaValue value);
void luna_mark_array(LunaValueArray* array);
void luna_mark_table(LunaTable* table);
void luna_mark_roots(LunaVM* vm);
void luna_trace_references(LunaVM* vm);
void luna_sweep(LunaVM* vm);
void luna_free_object(LunaObject* object);

// Memory management
void* luna_reallocate(void* pointer, size_t old_size, size_t new_size);
void luna_free_objects(LunaVM* vm);

// GC configuration
void luna_set_gc_threshold(LunaVM* vm, size_t threshold);
size_t luna_get_bytes_allocated(LunaVM* vm);

#endif // LUNA_GC_H
