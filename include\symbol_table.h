#ifndef LUNA_SYMBOL_TABLE_H
#define LUNA_SYMBOL_TABLE_H

#include "luna.h"
#include "value.h"

// Symbol types
typedef enum {
    SYMBOL_VARIABLE,
    SYMBOL_FUNCTION,
    SYMBOL_CLASS,
    SYMBOL_PARAMETER,
    SYMBOL_LOCAL,
    SYMBOL_GLOBAL,
    SYMBOL_UPVALUE
} LunaSymbolType;

// Symbol information
typedef struct {
    LunaString* name;
    LunaSymbolType type;
    LunaValue value;
    int depth;          // Scope depth (0 = global, 1+ = local)
    int index;          // Index in local/upvalue array
    bool is_captured;   // For upvalue analysis
    bool is_const;      // Whether the symbol is constant
    bool is_initialized; // Whether the variable has been initialized
} LunaSymbol;

// Symbol table entry
typedef struct LunaSymbolEntry {
    LunaSymbol symbol;
    struct LunaSymbolEntry* next;
} LunaSymbolEntry;

// Symbol table (hash table of symbols)
typedef struct {
    LunaSymbolEntry** buckets;
    int capacity;
    int count;
} LunaSymbolTable;

// Scope information
typedef struct LunaScope {
    LunaSymbolTable* symbols;
    int depth;
    int local_count;
    struct LunaScope* enclosing;
    bool is_function_scope;
    bool is_class_scope;
    bool is_loop_scope;
} LunaScope;

// Scope manager
typedef struct {
    LunaScope* current;
    LunaScope* global;
    int max_depth;
} LunaScopeManager;

// Symbol table operations
LunaSymbolTable* luna_create_symbol_table(void);
void luna_free_symbol_table(LunaSymbolTable* table);
bool luna_symbol_table_set(LunaSymbolTable* table, LunaString* name, LunaSymbol symbol);
bool luna_symbol_table_get(LunaSymbolTable* table, LunaString* name, LunaSymbol* symbol);
bool luna_symbol_table_delete(LunaSymbolTable* table, LunaString* name);
void luna_symbol_table_clear(LunaSymbolTable* table);

// Scope operations
LunaScope* luna_create_scope(LunaScope* enclosing, int depth);
void luna_free_scope(LunaScope* scope);
bool luna_scope_define(LunaScope* scope, LunaString* name, LunaSymbol symbol);
bool luna_scope_get(LunaScope* scope, LunaString* name, LunaSymbol* symbol);
bool luna_scope_get_local(LunaScope* scope, LunaString* name, LunaSymbol* symbol);
bool luna_scope_set(LunaScope* scope, LunaString* name, LunaValue value);

// Scope manager operations
void luna_init_scope_manager(LunaScopeManager* manager);
void luna_free_scope_manager(LunaScopeManager* manager);
void luna_push_scope(LunaScopeManager* manager, bool is_function, bool is_class, bool is_loop);
void luna_pop_scope(LunaScopeManager* manager);
bool luna_define_variable(LunaScopeManager* manager, LunaString* name, LunaValue value, bool is_const);
bool luna_get_variable(LunaScopeManager* manager, LunaString* name, LunaSymbol* symbol);
bool luna_set_variable(LunaScopeManager* manager, LunaString* name, LunaValue value);
bool luna_is_variable_defined(LunaScopeManager* manager, LunaString* name);

// Function and class management
bool luna_define_function(LunaScopeManager* manager, LunaString* name, LunaFunction* function);
bool luna_define_class(LunaScopeManager* manager, LunaString* name, LunaClass* klass);
bool luna_define_parameter(LunaScopeManager* manager, LunaString* name, int index);

// Upvalue resolution
int luna_resolve_upvalue(LunaScopeManager* manager, LunaString* name);
int luna_resolve_local(LunaScope* scope, LunaString* name);

// Scope queries
bool luna_is_in_function_scope(LunaScopeManager* manager);
bool luna_is_in_class_scope(LunaScopeManager* manager);
bool luna_is_in_loop_scope(LunaScopeManager* manager);
int luna_get_current_depth(LunaScopeManager* manager);

// Error checking
bool luna_check_variable_redefinition(LunaScopeManager* manager, LunaString* name);
bool luna_check_const_assignment(LunaScopeManager* manager, LunaString* name);
bool luna_check_uninitialized_variable(LunaScopeManager* manager, LunaString* name);

// Utility functions
uint32_t luna_hash_symbol_name(LunaString* name);
void luna_print_symbol_table(LunaSymbolTable* table);
void luna_print_scope(LunaScope* scope);

#endif // LUNA_SYMBOL_TABLE_H
