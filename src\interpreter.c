#include "interpreter.h"
#include "value.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <math.h>

void luna_init_interpreter(LunaInterpreter* interpreter, LunaVM* vm) {
    interpreter->vm = vm;
    interpreter->scope_manager = malloc(sizeof(LunaScopeManager));
    luna_init_scope_manager(interpreter->scope_manager);
    
    interpreter->has_error = false;
    interpreter->error.type = LUNA_ERROR_NONE;
    interpreter->error.message = NULL;
    
    interpreter->is_returning = false;
    interpreter->is_breaking = false;
    interpreter->is_continuing = false;
    interpreter->return_value = LUNA_NIL_VAL;
    
    interpreter->is_throwing = false;
    interpreter->exception_value = LUNA_NIL_VAL;
}

void luna_free_interpreter(LunaInterpreter* interpreter) {
    if (interpreter->scope_manager) {
        luna_free_scope_manager(interpreter->scope_manager);
        free(interpreter->scope_manager);
    }
    
    if (interpreter->error.message) {
        free(interpreter->error.message);
    }
}

LunaValue luna_evaluate(LunaInterpreter* interpreter, LunaASTNode* node) {
    if (node == NULL) return LUNA_NIL_VAL;
    
    switch (node->type) {
        // Expressions
        case AST_LITERAL_EXPR:
            return luna_evaluate_literal(interpreter, node);
        case AST_VARIABLE_EXPR:
            return luna_evaluate_variable(interpreter, node);
        case AST_BINARY_EXPR:
            return luna_evaluate_binary(interpreter, node);
        case AST_UNARY_EXPR:
            return luna_evaluate_unary(interpreter, node);
        case AST_CALL_EXPR:
            return luna_evaluate_call(interpreter, node);
        case AST_GET_EXPR:
            return luna_evaluate_get(interpreter, node);
        case AST_SET_EXPR:
            return luna_evaluate_set(interpreter, node);
        case AST_ARRAY_EXPR:
            return luna_evaluate_array(interpreter, node);
        case AST_INDEX_EXPR:
            return luna_evaluate_index(interpreter, node);
        case AST_ASSIGN_EXPR:
            return luna_evaluate_assign(interpreter, node);
        case AST_LOGICAL_EXPR:
            return luna_evaluate_logical(interpreter, node);
        case AST_CONDITIONAL_EXPR:
            return luna_evaluate_conditional(interpreter, node);
        case AST_THIS_EXPR:
            return luna_evaluate_this(interpreter, node);
        case AST_SUPER_EXPR:
            return luna_evaluate_super(interpreter, node);
            
        // Statements (execute and return nil)
        default:
            luna_execute_statement(interpreter, node);
            return LUNA_NIL_VAL;
    }
}

void luna_execute_statement(LunaInterpreter* interpreter, LunaASTNode* node) {
    if (node == NULL) return;
    if (luna_should_stop_execution(interpreter)) return;
    
    switch (node->type) {
        case AST_PROGRAM:
            luna_execute_program(interpreter, node);
            break;
        case AST_BLOCK:
            luna_execute_block(interpreter, node);
            break;
        case AST_EXPRESSION_STMT:
            luna_execute_expression_stmt(interpreter, node);
            break;
        case AST_VAR_DECLARATION:
            luna_execute_var_declaration(interpreter, node);
            break;
        case AST_FUNC_DECLARATION:
            luna_execute_func_declaration(interpreter, node);
            break;
        case AST_CLASS_DECLARATION:
            luna_execute_class_declaration(interpreter, node);
            break;
        case AST_IF_STMT:
            luna_execute_if_stmt(interpreter, node);
            break;
        case AST_WHILE_STMT:
            luna_execute_while_stmt(interpreter, node);
            break;
        case AST_FOR_STMT:
            luna_execute_for_stmt(interpreter, node);
            break;
        case AST_SWITCH_STMT:
            luna_execute_switch_stmt(interpreter, node);
            break;
        case AST_RETURN_STMT:
            luna_execute_return_stmt(interpreter, node);
            break;
        case AST_BREAK_STMT:
            luna_execute_break_stmt(interpreter, node);
            break;
        case AST_CONTINUE_STMT:
            luna_execute_continue_stmt(interpreter, node);
            break;
        case AST_TRY_STMT:
            luna_execute_try_stmt(interpreter, node);
            break;
        case AST_THROW_STMT:
            luna_execute_throw_stmt(interpreter, node);
            break;
        default:
            luna_interpreter_error(interpreter, "Unknown statement type");
            break;
    }
}

// Expression evaluation implementations
LunaValue luna_evaluate_literal(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter; // Unused
    return node->literal.value;
}

LunaValue luna_evaluate_variable(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaString* name = luna_copy_string(node->variable.name.start, node->variable.name.length);
    
    LunaSymbol symbol;
    if (luna_get_variable(interpreter->scope_manager, name, &symbol)) {
        if (!symbol.is_initialized) {
            luna_interpreter_runtime_error(interpreter, node, "Variable '%s' used before initialization", name->chars);
            return LUNA_NIL_VAL;
        }
        return symbol.value;
    }
    
    // Try global variables
    LunaValue value;
    if (luna_table_get(interpreter->vm->globals, name, &value)) {
        return value;
    }
    
    luna_interpreter_runtime_error(interpreter, node, "Undefined variable '%s'", name->chars);
    return LUNA_NIL_VAL;
}

LunaValue luna_evaluate_binary(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue left = luna_evaluate(interpreter, node->binary.left);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    LunaValue right = luna_evaluate(interpreter, node->binary.right);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    return luna_perform_binary_operation(interpreter, left, node->binary.operator, right);
}

LunaValue luna_evaluate_unary(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue operand = luna_evaluate(interpreter, node->unary.right);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    return luna_perform_unary_operation(interpreter, node->unary.operator, operand);
}

LunaValue luna_evaluate_assign(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue value = luna_evaluate(interpreter, node->assign.value);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    LunaString* name = luna_copy_string(node->assign.name.start, node->assign.name.length);
    
    // Check if it's a const variable
    if (luna_check_const_assignment(interpreter->scope_manager, name)) {
        luna_interpreter_runtime_error(interpreter, node, "Cannot assign to const variable '%s'", name->chars);
        return LUNA_NIL_VAL;
    }
    
    // Try to set in local scope first
    if (luna_set_variable(interpreter->scope_manager, name, value)) {
        return value;
    }
    
    // Try global scope
    if (luna_table_set(interpreter->vm->globals, name, value)) {
        return value;
    }
    
    luna_interpreter_runtime_error(interpreter, node, "Undefined variable '%s'", name->chars);
    return LUNA_NIL_VAL;
}

LunaValue luna_evaluate_logical(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue left = luna_evaluate(interpreter, node->logical.left);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    if (node->logical.operator.type == TOKEN_OR_OR) {
        if (luna_is_truthy(left)) return left;
    } else if (node->logical.operator.type == TOKEN_AND_AND) {
        if (!luna_is_truthy(left)) return left;
    }
    
    return luna_evaluate(interpreter, node->logical.right);
}

LunaValue luna_evaluate_array(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaArray* array = luna_new_array();
    
    for (int i = 0; i < node->array.elements->count; i++) {
        LunaValue element = luna_evaluate(interpreter, node->array.elements->nodes[i]);
        if (interpreter->has_error) return LUNA_NIL_VAL;
        luna_array_push(array, element);
    }
    
    return LUNA_OBJECT_VAL(array);
}

LunaValue luna_evaluate_index(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue array_value = luna_evaluate(interpreter, node->index.array);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    LunaValue index_value = luna_evaluate(interpreter, node->index.index);
    if (interpreter->has_error) return LUNA_NIL_VAL;
    
    if (!LUNA_IS_NUMBER(index_value)) {
        luna_interpreter_runtime_error(interpreter, node, "Array index must be a number");
        return LUNA_NIL_VAL;
    }
    
    int index = (int)LUNA_AS_NUMBER(index_value);
    
    if (LUNA_IS_ARRAY(array_value)) {
        LunaArray* array = LUNA_AS_ARRAY(array_value);
        if (index < 0 || index >= array->count) {
            luna_interpreter_runtime_error(interpreter, node, "Array index out of bounds");
            return LUNA_NIL_VAL;
        }
        return luna_array_get(array, index);
    } else if (LUNA_IS_STRING(array_value)) {
        LunaString* string = LUNA_AS_STRING(array_value);
        if (index < 0 || index >= string->length) {
            luna_interpreter_runtime_error(interpreter, node, "String index out of bounds");
            return LUNA_NIL_VAL;
        }
        char char_str[2] = {string->chars[index], '\0'};
        return LUNA_OBJECT_VAL(luna_copy_string(char_str, 1));
    }
    
    luna_interpreter_runtime_error(interpreter, node, "Only arrays and strings can be indexed");
    return LUNA_NIL_VAL;
}

// Statement execution implementations
void luna_execute_program(LunaInterpreter* interpreter, LunaASTNode* node) {
    for (int i = 0; i < node->program.statements->count; i++) {
        luna_execute_statement(interpreter, node->program.statements->nodes[i]);
        if (luna_should_stop_execution(interpreter)) break;
    }
}

void luna_execute_block(LunaInterpreter* interpreter, LunaASTNode* node) {
    luna_push_scope(interpreter->scope_manager, false, false, false);
    
    for (int i = 0; i < node->block.statements->count; i++) {
        luna_execute_statement(interpreter, node->block.statements->nodes[i]);
        if (luna_should_stop_execution(interpreter)) break;
    }
    
    luna_pop_scope(interpreter->scope_manager);
}

void luna_execute_expression_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    luna_evaluate(interpreter, node->expression_stmt.expression);
}

void luna_execute_var_declaration(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue value = LUNA_NIL_VAL;
    
    if (node->var_declaration.initializer != NULL) {
        value = luna_evaluate(interpreter, node->var_declaration.initializer);
        if (interpreter->has_error) return;
    }
    
    LunaString* name = luna_copy_string(node->var_declaration.name.start, node->var_declaration.name.length);
    
    // Check for redefinition in current scope
    if (luna_check_variable_redefinition(interpreter->scope_manager, name)) {
        luna_interpreter_runtime_error(interpreter, node, "Variable '%s' already defined in this scope", name->chars);
        return;
    }
    
    luna_define_variable(interpreter->scope_manager, name, value, node->var_declaration.is_const);
}

void luna_execute_if_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue condition = luna_evaluate(interpreter, node->if_stmt.condition);
    if (interpreter->has_error) return;

    if (luna_is_truthy(condition)) {
        luna_execute_statement(interpreter, node->if_stmt.then_branch);
    } else if (node->if_stmt.else_branch != NULL) {
        luna_execute_statement(interpreter, node->if_stmt.else_branch);
    }
}

void luna_execute_while_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    luna_push_scope(interpreter->scope_manager, false, false, true); // Loop scope

    while (true) {
        LunaValue condition = luna_evaluate(interpreter, node->while_stmt.condition);
        if (interpreter->has_error) break;

        if (!luna_is_truthy(condition)) break;

        luna_execute_statement(interpreter, node->while_stmt.body);

        if (interpreter->is_breaking) {
            interpreter->is_breaking = false;
            break;
        }

        if (interpreter->is_continuing) {
            interpreter->is_continuing = false;
            continue;
        }

        if (interpreter->is_returning || interpreter->is_throwing) break;
    }

    luna_pop_scope(interpreter->scope_manager);
}

void luna_execute_return_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue value = LUNA_NIL_VAL;

    if (node->return_stmt.expression != NULL) {
        value = luna_evaluate(interpreter, node->return_stmt.expression);
        if (interpreter->has_error) return;
    }

    if (!luna_is_in_function_scope(interpreter->scope_manager)) {
        luna_interpreter_runtime_error(interpreter, node, "Cannot return from outside a function");
        return;
    }

    luna_set_return(interpreter, value);
}

void luna_execute_break_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    if (!luna_is_in_loop_scope(interpreter->scope_manager)) {
        luna_interpreter_runtime_error(interpreter, node, "Cannot break from outside a loop");
        return;
    }

    luna_set_break(interpreter);
}

void luna_execute_continue_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    if (!luna_is_in_loop_scope(interpreter->scope_manager)) {
        luna_interpreter_runtime_error(interpreter, node, "Cannot continue from outside a loop");
        return;
    }

    luna_set_continue(interpreter);
}

// Utility functions
bool luna_is_truthy(LunaValue value) {
    if (LUNA_IS_NIL(value)) return false;
    if (LUNA_IS_BOOL(value)) return LUNA_AS_BOOL(value);
    return true;
}

bool luna_is_equal(LunaValue a, LunaValue b) {
    return luna_values_equal(a, b);
}

LunaValue luna_perform_binary_operation(LunaInterpreter* interpreter, LunaValue left, LunaToken operator, LunaValue right) {
    switch (operator.type) {
        case TOKEN_PLUS:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_NUMBER_VAL(LUNA_AS_NUMBER(left) + LUNA_AS_NUMBER(right));
            } else if (LUNA_IS_STRING(left) && LUNA_IS_STRING(right)) {
                LunaString* left_str = LUNA_AS_STRING(left);
                LunaString* right_str = LUNA_AS_STRING(right);
                int length = left_str->length + right_str->length;
                char* chars = malloc(length + 1);
                memcpy(chars, left_str->chars, left_str->length);
                memcpy(chars + left_str->length, right_str->chars, right_str->length);
                chars[length] = '\0';
                return LUNA_OBJECT_VAL(luna_take_string(chars, length));
            }
            luna_interpreter_error(interpreter, "Operands must be two numbers or two strings");
            return LUNA_NIL_VAL;

        case TOKEN_MINUS:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_NUMBER_VAL(LUNA_AS_NUMBER(left) - LUNA_AS_NUMBER(right));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_STAR:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_NUMBER_VAL(LUNA_AS_NUMBER(left) * LUNA_AS_NUMBER(right));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_SLASH:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                double right_val = LUNA_AS_NUMBER(right);
                if (right_val == 0) {
                    luna_interpreter_error(interpreter, "Division by zero");
                    return LUNA_NIL_VAL;
                }
                return LUNA_NUMBER_VAL(LUNA_AS_NUMBER(left) / right_val);
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_PERCENT:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                double right_val = LUNA_AS_NUMBER(right);
                if (right_val == 0) {
                    luna_interpreter_error(interpreter, "Division by zero");
                    return LUNA_NIL_VAL;
                }
                return LUNA_NUMBER_VAL(fmod(LUNA_AS_NUMBER(left), right_val));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_GREATER:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_BOOL_VAL(LUNA_AS_NUMBER(left) > LUNA_AS_NUMBER(right));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_GREATER_EQUAL:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_BOOL_VAL(LUNA_AS_NUMBER(left) >= LUNA_AS_NUMBER(right));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_LESS:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_BOOL_VAL(LUNA_AS_NUMBER(left) < LUNA_AS_NUMBER(right));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_LESS_EQUAL:
            if (LUNA_IS_NUMBER(left) && LUNA_IS_NUMBER(right)) {
                return LUNA_BOOL_VAL(LUNA_AS_NUMBER(left) <= LUNA_AS_NUMBER(right));
            }
            luna_interpreter_error(interpreter, "Operands must be numbers");
            return LUNA_NIL_VAL;

        case TOKEN_EQUAL_EQUAL:
            return LUNA_BOOL_VAL(luna_is_equal(left, right));

        case TOKEN_BANG_EQUAL:
            return LUNA_BOOL_VAL(!luna_is_equal(left, right));

        default:
            luna_interpreter_error(interpreter, "Unknown binary operator");
            return LUNA_NIL_VAL;
    }
}

LunaValue luna_perform_unary_operation(LunaInterpreter* interpreter, LunaToken operator, LunaValue operand) {
    switch (operator.type) {
        case TOKEN_MINUS:
            if (LUNA_IS_NUMBER(operand)) {
                return LUNA_NUMBER_VAL(-LUNA_AS_NUMBER(operand));
            }
            luna_interpreter_error(interpreter, "Operand must be a number");
            return LUNA_NIL_VAL;

        case TOKEN_BANG:
            return LUNA_BOOL_VAL(!luna_is_truthy(operand));

        default:
            luna_interpreter_error(interpreter, "Unknown unary operator");
            return LUNA_NIL_VAL;
    }
}

// Error handling
void luna_interpreter_error(LunaInterpreter* interpreter, const char* format, ...) {
    va_list args;
    va_start(args, format);

    char message[256];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);

    interpreter->has_error = true;
    interpreter->error.type = LUNA_ERROR_RUNTIME;
    if (interpreter->error.message) {
        free(interpreter->error.message);
    }
    interpreter->error.message = strdup(message);
}

void luna_interpreter_runtime_error(LunaInterpreter* interpreter, LunaASTNode* node, const char* format, ...) {
    va_list args;
    va_start(args, format);

    char message[256];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);

    interpreter->has_error = true;
    interpreter->error.type = LUNA_ERROR_RUNTIME;
    interpreter->error.line = node->line;
    interpreter->error.column = node->column;
    if (interpreter->error.message) {
        free(interpreter->error.message);
    }
    interpreter->error.message = strdup(message);
}

// Control flow helpers
void luna_set_return(LunaInterpreter* interpreter, LunaValue value) {
    interpreter->is_returning = true;
    interpreter->return_value = value;
}

void luna_set_break(LunaInterpreter* interpreter) {
    interpreter->is_breaking = true;
}

void luna_set_continue(LunaInterpreter* interpreter) {
    interpreter->is_continuing = true;
}

void luna_clear_control_flow(LunaInterpreter* interpreter) {
    interpreter->is_returning = false;
    interpreter->is_breaking = false;
    interpreter->is_continuing = false;
    interpreter->return_value = LUNA_NIL_VAL;
}

bool luna_should_stop_execution(LunaInterpreter* interpreter) {
    return interpreter->has_error || interpreter->is_returning ||
           interpreter->is_breaking || interpreter->is_continuing ||
           interpreter->is_throwing;
}

// Exception handling
void luna_throw_exception(LunaInterpreter* interpreter, LunaValue exception) {
    interpreter->is_throwing = true;
    interpreter->exception_value = exception;
}

void luna_clear_exception(LunaInterpreter* interpreter) {
    interpreter->is_throwing = false;
    interpreter->exception_value = LUNA_NIL_VAL;
}

// Placeholder implementations for missing functions
LunaValue luna_evaluate_call(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue callee = luna_evaluate(interpreter, node->call.callee);
    if (interpreter->has_error) return LUNA_NIL_VAL;

    // Evaluate arguments
    LunaValue* args = malloc(sizeof(LunaValue) * node->call.arguments->count);
    for (int i = 0; i < node->call.arguments->count; i++) {
        args[i] = luna_evaluate(interpreter, node->call.arguments->nodes[i]);
        if (interpreter->has_error) {
            free(args);
            return LUNA_NIL_VAL;
        }
    }

    LunaValue result = LUNA_NIL_VAL;

    if (LUNA_IS_FUNCTION(callee)) {
        result = luna_call_function(interpreter, LUNA_AS_FUNCTION(callee), node->call.arguments->count, args);
    } else if (LUNA_IS_NATIVE_FUNCTION(callee)) {
        LunaNativeFunction* native = LUNA_AS_NATIVE_FUNCTION(callee);
        result = native->function(node->call.arguments->count, args);
    } else {
        luna_interpreter_runtime_error(interpreter, node, "Can only call functions");
    }

    free(args);
    return result;
}

LunaValue luna_call_function(LunaInterpreter* interpreter, LunaFunction* function, int arg_count, LunaValue* args) {
    // Check arity
    if (arg_count != function->arity) {
        luna_interpreter_error(interpreter, "Function '%s' expects %d arguments, got %d",
                              function->name->chars, function->arity, arg_count);
        return LUNA_NIL_VAL;
    }

    // Create new scope for function
    luna_push_scope(interpreter->scope_manager, true, false, false); // Function scope

    // Bind parameters
    for (int i = 0; i < function->arity; i++) {
        luna_define_variable(interpreter->scope_manager, function->param_names[i], args[i], false);
    }

    // Execute function body
    luna_execute_statement(interpreter, function->body);

    LunaValue result = LUNA_NIL_VAL;
    if (interpreter->is_returning) {
        result = interpreter->return_value;
        interpreter->is_returning = false;
        interpreter->return_value = LUNA_NIL_VAL;
    }

    luna_pop_scope(interpreter->scope_manager);
    return result;
}

LunaValue luna_evaluate_get(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement property access
    return LUNA_NIL_VAL;
}

LunaValue luna_evaluate_set(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement property assignment
    return LUNA_NIL_VAL;
}

LunaValue luna_evaluate_conditional(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue condition = luna_evaluate(interpreter, node->conditional.condition);
    if (interpreter->has_error) return LUNA_NIL_VAL;

    if (luna_is_truthy(condition)) {
        return luna_evaluate(interpreter, node->conditional.then_expr);
    } else {
        return luna_evaluate(interpreter, node->conditional.else_expr);
    }
}

LunaValue luna_evaluate_this(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement 'this' keyword
    return LUNA_NIL_VAL;
}

LunaValue luna_evaluate_super(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement 'super' keyword
    return LUNA_NIL_VAL;
}

void luna_execute_func_declaration(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaString* name = luna_copy_string(node->func_declaration.name.start, node->func_declaration.name.length);

    // Create function object
    LunaFunction* function = luna_new_function();
    function->name = name;
    function->arity = node->func_declaration.params->count;
    function->body = node->func_declaration.body;

    // Store parameter names
    function->param_names = malloc(sizeof(LunaString*) * function->arity);
    for (int i = 0; i < function->arity; i++) {
        LunaToken* param = &node->func_declaration.params->tokens[i];
        function->param_names[i] = luna_copy_string(param->start, param->length);
    }

    // Define the function in current scope
    luna_define_variable(interpreter->scope_manager, name, LUNA_OBJECT_VAL(function), false);
}

void luna_execute_class_declaration(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement class declarations
}

void luna_execute_for_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    luna_push_scope(interpreter->scope_manager, false, false, true); // Loop scope

    // Execute initializer
    if (node->for_stmt.initializer != NULL) {
        luna_execute_statement(interpreter, node->for_stmt.initializer);
        if (interpreter->has_error) {
            luna_pop_scope(interpreter->scope_manager);
            return;
        }
    }

    while (true) {
        // Check condition
        if (node->for_stmt.condition != NULL) {
            LunaValue condition = luna_evaluate(interpreter, node->for_stmt.condition);
            if (interpreter->has_error) break;

            if (!luna_is_truthy(condition)) break;
        }

        // Execute body
        luna_execute_statement(interpreter, node->for_stmt.body);

        if (interpreter->is_breaking) {
            interpreter->is_breaking = false;
            break;
        }

        if (interpreter->is_continuing) {
            interpreter->is_continuing = false;
        } else if (interpreter->is_returning || interpreter->is_throwing) {
            break;
        }

        // Execute increment
        if (node->for_stmt.increment != NULL) {
            luna_evaluate(interpreter, node->for_stmt.increment);
            if (interpreter->has_error) break;
        }
    }

    luna_pop_scope(interpreter->scope_manager);
}

void luna_execute_switch_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    LunaValue switch_value = luna_evaluate(interpreter, node->switch_stmt.expression);
    if (interpreter->has_error) return;

    bool matched = false;
    bool fall_through = false;

    // Check each case
    for (int i = 0; i < node->switch_stmt.cases->count; i++) {
        LunaASTNode* case_node = node->switch_stmt.cases->nodes[i];

        if (case_node->type == AST_CASE_STMT) {
            if (!matched && !fall_through) {
                LunaValue case_value = luna_evaluate(interpreter, case_node->case_stmt.value);
                if (interpreter->has_error) return;

                if (luna_is_equal(switch_value, case_value)) {
                    matched = true;
                }
            }

            if (matched || fall_through) {
                for (int j = 0; j < case_node->case_stmt.statements->count; j++) {
                    luna_execute_statement(interpreter, case_node->case_stmt.statements->nodes[j]);
                    if (luna_should_stop_execution(interpreter)) {
                        if (interpreter->is_breaking) {
                            interpreter->is_breaking = false;
                            return;
                        }
                        return;
                    }
                }
                fall_through = true; // Continue to next case unless break
            }
        } else if (case_node->type == AST_DEFAULT_STMT) {
            if (!matched || fall_through) {
                for (int j = 0; j < case_node->default_stmt.statements->count; j++) {
                    luna_execute_statement(interpreter, case_node->default_stmt.statements->nodes[j]);
                    if (luna_should_stop_execution(interpreter)) {
                        if (interpreter->is_breaking) {
                            interpreter->is_breaking = false;
                            return;
                        }
                        return;
                    }
                }
            }
        }
    }
}

void luna_execute_try_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement try-catch statements
}

void luna_execute_throw_stmt(LunaInterpreter* interpreter, LunaASTNode* node) {
    (void)interpreter;
    (void)node;
    // TODO: Implement throw statements
}
