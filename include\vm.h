#ifndef LUNA_VM_H
#define LUNA_VM_H

#include "luna.h"
#include "value.h"
#include "ast.h"

// Bytecode instructions for JIT compilation
typedef enum {
    OP_CONSTANT,
    OP_NIL,
    OP_TRUE,
    OP_FALSE,
    OP_POP,
    OP_GET_LOCAL,
    OP_SET_LOCAL,
    OP_GET_GLOBAL,
    OP_DEFINE_GLOBAL,
    OP_SET_GLOBAL,
    OP_GET_UPVALUE,
    OP_SET_UPVALUE,
    OP_GET_PROPERTY,
    OP_SET_PROPERTY,
    OP_GET_SUPER,
    OP_EQUAL,
    OP_GREATER,
    OP_LESS,
    OP_ADD,
    OP_SUBTRACT,
    OP_MULTIPLY,
    OP_DIVIDE,
    OP_MODULO,
    OP_NOT,
    OP_NEGATE,
    OP_PRINT,
    OP_JUMP,
    OP_JUMP_IF_FALSE,
    OP_LOOP,
    OP_CALL,
    OP_INVOKE,
    OP_SUPER_INVOKE,
    OP_CLOSURE,
    OP_CLOSE_UPVALUE,
    OP_RETURN,
    OP_CLASS,
    OP_INHERIT,
    OP_METHOD,
    OP_ARRAY,
    OP_INDEX_GET,
    OP_INDEX_SET,
    OP_THROW,
    OP_TRY_BEGIN,
    OP_TRY_END,
    OP_CATCH,
    OP_FINALLY
} LunaOpCode;

// Bytecode chunk
typedef struct {
    int count;
    int capacity;
    uint8_t* code;
    int* lines;
    LunaValueArray constants;
} LunaChunk;

// Call frame for function calls
typedef struct {
    LunaClosure* closure;
    uint8_t* ip;
    LunaValue* slots;
} LunaCallFrame;

// Exception handler
typedef struct LunaExceptionHandler {
    int try_start;
    int try_end;
    int catch_start;
    int finally_start;
    struct LunaExceptionHandler* next;
} LunaExceptionHandler;

// Virtual machine state
struct LunaVM {
    LunaCallFrame frames[LUNA_MAX_CALL_STACK_DEPTH];
    int frame_count;
    
    LunaValue stack[LUNA_MAX_CALL_STACK_DEPTH * 256];
    LunaValue* stack_top;
    
    LunaTable* globals;
    LunaTable* strings;
    LunaString* init_string;
    
    LunaUpvalue* open_upvalues;
    
    // Garbage collection
    size_t bytes_allocated;
    size_t next_gc;
    LunaObject* objects;
    int gray_count;
    int gray_capacity;
    LunaObject** gray_stack;
    
    // Exception handling
    LunaExceptionHandler* exception_handlers;
    LunaValue current_exception;
    bool has_exception;
    
    // JIT compilation
    bool jit_enabled;
    int jit_threshold;
    LunaTable* compiled_functions;
    
    // Error reporting
    LunaError last_error;
    bool has_error;
};

// Chunk operations
void luna_init_chunk(LunaChunk* chunk);
void luna_free_chunk(LunaChunk* chunk);
void luna_write_chunk(LunaChunk* chunk, uint8_t byte, int line);
int luna_add_constant(LunaChunk* chunk, LunaValue value);

// VM operations
void luna_init_vm(LunaVM* vm);
void luna_free_vm(LunaVM* vm);
LunaValue luna_interpret(LunaVM* vm, const char* source);
LunaValue luna_interpret_ast(LunaVM* vm, LunaASTNode* ast);

// Stack operations
void luna_push(LunaVM* vm, LunaValue value);
LunaValue luna_pop(LunaVM* vm);
LunaValue luna_peek(LunaVM* vm, int distance);

// Error handling
void luna_runtime_error(LunaVM* vm, const char* format, ...);
void luna_set_error(LunaVM* vm, LunaErrorType type, const char* format, ...);
void luna_clear_error(LunaVM* vm);

// Exception handling
void luna_throw_exception(LunaVM* vm, LunaValue exception);
void luna_push_exception_handler(LunaVM* vm, int try_start, int try_end, int catch_start, int finally_start);
void luna_pop_exception_handler(LunaVM* vm);

// Native function registration
void luna_define_native(LunaVM* vm, const char* name, LunaNativeFn function);

// Global variable operations
void luna_define_global(LunaVM* vm, const char* name, LunaValue value);
bool luna_get_global(LunaVM* vm, const char* name, LunaValue* value);
bool luna_set_global(LunaVM* vm, const char* name, LunaValue value);

// JIT compilation
void luna_enable_jit(LunaVM* vm, int threshold);
void luna_disable_jit(LunaVM* vm);
bool luna_should_compile(LunaVM* vm, LunaFunction* function);
void luna_compile_function(LunaVM* vm, LunaFunction* function);

// Debugging
void luna_disassemble_chunk(LunaChunk* chunk, const char* name);
int luna_disassemble_instruction(LunaChunk* chunk, int offset);
void luna_print_stack(LunaVM* vm);

// Internal VM functions
void luna_reset_stack(LunaVM* vm);
void luna_define_native_functions(LunaVM* vm);

// Utility macros
#define LUNA_STACK_MAX (LUNA_MAX_CALL_STACK_DEPTH * 256)

#endif // LUNA_VM_H
