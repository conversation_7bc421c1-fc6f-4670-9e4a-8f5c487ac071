#ifndef LUNA_LEXER_H
#define LUNA_LEXER_H

#include "luna.h"

// Lexer state
typedef struct {
    const char* start;
    const char* current;
    int line;
    int column;
    const char* source;
    bool had_error;
    LunaError error;
} LunaLexer;

// <PERSON><PERSON> functions
void luna_init_lexer(LunaL<PERSON>er* lexer, const char* source);
LunaToken luna_scan_token(LunaLexer* lexer);
bool luna_lexer_is_at_end(LunaLexer* lexer);
void luna_lexer_error(LunaLex<PERSON>* lexer, const char* message);

// Token utilities
LunaToken luna_make_token(<PERSON><PERSON><PERSON>er* lexer, LunaTokenType type);
LunaToken luna_error_token(Luna<PERSON><PERSON>er* lexer, const char* message);
bool luna_match(LunaLexer* lexer, char expected);
char luna_advance(LunaLexer* lexer);
char luna_peek(LunaLexer* lexer);
char luna_peek_next(LunaLexer* lexer);
void luna_skip_whitespace(LunaLexer* lexer);

// Token type checking
LunaTokenType luna_check_keyword(LunaLexer* lexer, int start, int length, const char* rest, LunaTokenType type);
LunaTokenType luna_identifier_type(LunaLexer* lexer);

// Literal parsing
LunaToken luna_string(LunaLexer* lexer);
LunaToken luna_number(LunaLexer* lexer);

// Character classification
bool luna_is_alpha(char c);
bool luna_is_digit(char c);
bool luna_is_alnum(char c);

#endif // LUNA_LEXER_H
