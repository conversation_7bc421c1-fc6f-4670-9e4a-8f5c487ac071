#include "vm.h"
#include "gc.h"
#include "lexer.h"
#include "parser.h"
#include "value.h"
#include "stdlib.h"
#include "interpreter.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <time.h>

// Global VM instance for GC
LunaVM* current_vm = NULL;

// Chunk operations
void luna_init_chunk(LunaChunk* chunk) {
    chunk->count = 0;
    chunk->capacity = 0;
    chunk->code = NULL;
    chunk->lines = NULL;
    luna_init_value_array(&chunk->constants);
}

void luna_free_chunk(LunaChunk* chunk) {
    free(chunk->code);
    free(chunk->lines);
    luna_free_value_array(&chunk->constants);
    luna_init_chunk(chunk);
}

void luna_write_chunk(LunaChunk* chunk, uint8_t byte, int line) {
    if (chunk->capacity < chunk->count + 1) {
        int old_capacity = chunk->capacity;
        chunk->capacity = old_capacity < 8 ? 8 : old_capacity * 2;
        chunk->code = realloc(chunk->code, sizeof(uint8_t) * chunk->capacity);
        chunk->lines = realloc(chunk->lines, sizeof(int) * chunk->capacity);
    }
    
    chunk->code[chunk->count] = byte;
    chunk->lines[chunk->count] = line;
    chunk->count++;
}

int luna_add_constant(LunaChunk* chunk, LunaValue value) {
    luna_push(current_vm, value);
    luna_write_value_array(&chunk->constants, value);
    luna_pop(current_vm);
    return chunk->constants.count - 1;
}

// VM operations
void luna_init_vm(LunaVM* vm) {
    luna_reset_stack(vm);
    vm->objects = NULL;
    vm->bytes_allocated = 0;
    vm->next_gc = LUNA_INITIAL_GC_THRESHOLD;
    
    vm->gray_count = 0;
    vm->gray_capacity = 0;
    vm->gray_stack = NULL;
    
    vm->globals = luna_new_table();
    vm->strings = luna_new_table();
    vm->init_string = NULL;
    vm->open_upvalues = NULL;
    
    vm->exception_handlers = NULL;
    vm->current_exception = LUNA_NIL_VAL;
    vm->has_exception = false;
    
    vm->jit_enabled = false;
    vm->jit_threshold = 100;
    vm->compiled_functions = NULL;
    
    vm->has_error = false;
    vm->last_error.type = LUNA_ERROR_NONE;
    vm->last_error.message = NULL;
    
    current_vm = vm;
    
    // Initialize built-in strings
    vm->init_string = luna_copy_string("init", 4);

    // Initialize standard library
    luna_init_stdlib(vm);
}

void luna_free_vm(LunaVM* vm) {
    luna_free_objects(vm);
    current_vm = NULL;
}

static void luna_reset_stack(LunaVM* vm) {
    vm->stack_top = vm->stack;
    vm->frame_count = 0;
    vm->open_upvalues = NULL;
}

void luna_push(LunaVM* vm, LunaValue value) {
    *vm->stack_top = value;
    vm->stack_top++;
}

LunaValue luna_pop(LunaVM* vm) {
    vm->stack_top--;
    return *vm->stack_top;
}

LunaValue luna_peek(LunaVM* vm, int distance) {
    return vm->stack_top[-1 - distance];
}

static bool luna_call(LunaVM* vm, LunaClosure* closure, int arg_count) {
    if (arg_count != closure->function->arity) {
        luna_runtime_error(vm, "Expected %d arguments but got %d",
                          closure->function->arity, arg_count);
        return false;
    }
    
    if (vm->frame_count == LUNA_MAX_CALL_STACK_DEPTH) {
        luna_runtime_error(vm, "Stack overflow");
        return false;
    }
    
    LunaCallFrame* frame = &vm->frames[vm->frame_count++];
    frame->closure = closure;
    frame->ip = closure->function->chunk->code;
    frame->slots = vm->stack_top - arg_count - 1;
    return true;
}

static bool luna_call_value(LunaVM* vm, LunaValue callee, int arg_count) {
    if (LUNA_IS_OBJECT(callee)) {
        switch (LUNA_OBJ_TYPE(callee)) {
            case LUNA_OBJ_CLOSURE:
                return luna_call(vm, LUNA_AS_CLOSURE(callee), arg_count);
            case LUNA_OBJ_NATIVE_FUNCTION: {
                LunaNativeFn native = LUNA_AS_NATIVE(callee);
                LunaValue result = native(arg_count, vm->stack_top - arg_count);
                vm->stack_top -= arg_count + 1;
                luna_push(vm, result);
                return true;
            }
            case LUNA_OBJ_CLASS: {
                LunaClass* klass = LUNA_AS_CLASS(callee);
                vm->stack_top[-arg_count - 1] = LUNA_OBJECT_VAL(luna_new_instance(klass));
                
                LunaValue initializer;
                if (luna_table_get(klass->methods, vm->init_string, &initializer)) {
                    return luna_call(vm, LUNA_AS_CLOSURE(initializer), arg_count);
                } else if (arg_count != 0) {
                    luna_runtime_error(vm, "Expected 0 arguments but got %d", arg_count);
                    return false;
                }
                return true;
            }
            case LUNA_OBJ_BOUND_METHOD: {
                LunaBoundMethod* bound = LUNA_AS_BOUND_METHOD(callee);
                vm->stack_top[-arg_count - 1] = bound->receiver;
                return luna_call(vm, bound->method, arg_count);
            }
            default:
                break; // Non-callable object type
        }
    }
    
    luna_runtime_error(vm, "Can only call functions and classes");
    return false;
}

static LunaUpvalue* luna_capture_upvalue(LunaVM* vm, LunaValue* local) {
    LunaUpvalue* prev_upvalue = NULL;
    LunaUpvalue* upvalue = vm->open_upvalues;
    
    while (upvalue != NULL && upvalue->location > local) {
        prev_upvalue = upvalue;
        upvalue = upvalue->next;
    }
    
    if (upvalue != NULL && upvalue->location == local) {
        return upvalue;
    }
    
    LunaUpvalue* created_upvalue = luna_new_upvalue(local);
    created_upvalue->next = upvalue;
    
    if (prev_upvalue == NULL) {
        vm->open_upvalues = created_upvalue;
    } else {
        prev_upvalue->next = created_upvalue;
    }
    
    return created_upvalue;
}

static void luna_close_upvalues(LunaVM* vm, LunaValue* last) {
    while (vm->open_upvalues != NULL && vm->open_upvalues->location >= last) {
        LunaUpvalue* upvalue = vm->open_upvalues;
        upvalue->closed = *upvalue->location;
        upvalue->location = &upvalue->closed;
        vm->open_upvalues = upvalue->next;
    }
}

static void luna_define_method(LunaVM* vm, LunaString* name) {
    LunaValue method = luna_peek(vm, 0);
    LunaClass* klass = LUNA_AS_CLASS(luna_peek(vm, 1));
    luna_table_set(klass->methods, name, method);
    luna_pop(vm);
}

static bool luna_bind_method(LunaVM* vm, LunaClass* klass, LunaString* name) {
    LunaValue method;
    if (!luna_table_get(klass->methods, name, &method)) {
        luna_runtime_error(vm, "Undefined property '%s'", name->chars);
        return false;
    }
    
    LunaBoundMethod* bound = luna_new_bound_method(luna_peek(vm, 0), LUNA_AS_CLOSURE(method));
    luna_pop(vm);
    luna_push(vm, LUNA_OBJECT_VAL(bound));
    return true;
}

static bool luna_invoke_from_class(LunaVM* vm, LunaClass* klass, LunaString* name, int arg_count) {
    LunaValue method;
    if (!luna_table_get(klass->methods, name, &method)) {
        luna_runtime_error(vm, "Undefined property '%s'", name->chars);
        return false;
    }
    
    return luna_call(vm, LUNA_AS_CLOSURE(method), arg_count);
}

static bool luna_invoke(LunaVM* vm, LunaString* name, int arg_count) {
    LunaValue receiver = luna_peek(vm, arg_count);
    
    if (!LUNA_IS_INSTANCE(receiver)) {
        luna_runtime_error(vm, "Only instances have methods");
        return false;
    }
    
    LunaInstance* instance = LUNA_AS_INSTANCE(receiver);
    
    LunaValue value;
    if (luna_table_get(instance->fields, name, &value)) {
        vm->stack_top[-arg_count - 1] = value;
        return luna_call_value(vm, value, arg_count);
    }
    
    return luna_invoke_from_class(vm, instance->klass, name, arg_count);
}

// Error handling
void luna_runtime_error(LunaVM* vm, const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    char message[256];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    
    fprintf(stderr, "Runtime Error: %s\n", message);
    
    for (int i = vm->frame_count - 1; i >= 0; i--) {
        LunaCallFrame* frame = &vm->frames[i];
        LunaFunction* function = frame->closure->function;
        size_t instruction = frame->ip - function->chunk->code - 1;
        fprintf(stderr, "[line %d] in ", function->chunk->lines[instruction]);
        if (function->name == NULL) {
            fprintf(stderr, "script\n");
        } else {
            fprintf(stderr, "%s()\n", function->name->chars);
        }
    }
    
    luna_reset_stack(vm);
    
    vm->has_error = true;
    vm->last_error.type = LUNA_ERROR_RUNTIME;
    vm->last_error.message = strdup(message);
}

void luna_set_error(LunaVM* vm, LunaErrorType type, const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    char message[256];
    vsnprintf(message, sizeof(message), format, args);
    va_end(args);
    
    vm->has_error = true;
    vm->last_error.type = type;
    if (vm->last_error.message) {
        free(vm->last_error.message);
    }
    vm->last_error.message = strdup(message);
}

void luna_clear_error(LunaVM* vm) {
    vm->has_error = false;
    vm->last_error.type = LUNA_ERROR_NONE;
    if (vm->last_error.message) {
        free(vm->last_error.message);
        vm->last_error.message = NULL;
    }
}



void luna_define_native(LunaVM* vm, const char* name, LunaNativeFn function) {
    luna_push(vm, LUNA_OBJECT_VAL(luna_copy_string(name, strlen(name))));
    luna_push(vm, LUNA_OBJECT_VAL(luna_new_native_function(function, LUNA_AS_STRING(vm->stack[0]))));
    luna_table_set(vm->globals, LUNA_AS_STRING(vm->stack[0]), vm->stack[1]);
    luna_pop(vm);
    luna_pop(vm);
}

// Global variable operations
void luna_define_global(LunaVM* vm, const char* name, LunaValue value) {
    luna_push(vm, LUNA_OBJECT_VAL(luna_copy_string(name, strlen(name))));
    luna_push(vm, value);
    luna_table_set(vm->globals, LUNA_AS_STRING(vm->stack[0]), value);
    luna_pop(vm);
    luna_pop(vm);
}

bool luna_get_global(LunaVM* vm, const char* name, LunaValue* value) {
    LunaString* name_string = luna_copy_string(name, strlen(name));
    return luna_table_get(vm->globals, name_string, value);
}

bool luna_set_global(LunaVM* vm, const char* name, LunaValue value) {
    LunaString* name_string = luna_copy_string(name, strlen(name));
    return luna_table_set(vm->globals, name_string, value);
}

// Main interpretation function
LunaValue luna_interpret(LunaVM* vm, const char* source) {
    LunaLexer lexer;
    luna_init_lexer(&lexer, source);

    LunaParser parser;
    luna_init_parser(&parser, &lexer);

    LunaASTNode* ast = luna_parse(&parser);

    if (parser.had_error) {
        luna_set_error(vm, LUNA_ERROR_SYNTAX, "Parse error");
        return LUNA_NIL_VAL;
    }

    LunaValue result = luna_interpret_ast(vm, ast);
    luna_free_ast_node(ast);
    return result;
}

// Interpret AST using the interpreter
LunaValue luna_interpret_ast(LunaVM* vm, LunaASTNode* ast) {
    LunaInterpreter interpreter;
    luna_init_interpreter(&interpreter, vm);

    LunaValue result = luna_evaluate(&interpreter, ast);

    if (interpreter.has_error) {
        luna_set_error(vm, interpreter.error.type, "%s", interpreter.error.message);
        result = LUNA_NIL_VAL;
    }

    luna_free_interpreter(&interpreter);
    return result;
}
