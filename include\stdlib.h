#ifndef LUNA_STDLIB_H
#define LUNA_STDLIB_H

#include "luna.h"
#include "vm.h"

// Core built-in functions
LunaValue luna_builtin_print(int arg_count, LunaValue* args);
LunaValue luna_builtin_println(int arg_count, LunaValue* args);
LunaValue luna_builtin_input(int arg_count, LunaValue* args);
LunaValue luna_builtin_clock(int arg_count, LunaValue* args);
LunaValue luna_builtin_type(int arg_count, LunaValue* args);
LunaValue luna_builtin_len(int arg_count, LunaValue* args);
LunaValue luna_builtin_str(int arg_count, LunaValue* args);
LunaValue luna_builtin_num(int arg_count, LunaValue* args);
LunaValue luna_builtin_bool(int arg_count, LunaValue* args);

// Math functions
LunaValue luna_builtin_abs(int arg_count, LunaValue* args);
LunaValue luna_builtin_min(int arg_count, LunaValue* args);
LunaValue luna_builtin_max(int arg_count, LunaValue* args);
LunaValue luna_builtin_floor(int arg_count, LunaValue* args);
LunaValue luna_builtin_ceil(int arg_count, LunaValue* args);
LunaValue luna_builtin_round(int arg_count, LunaValue* args);
LunaValue luna_builtin_sqrt(int arg_count, LunaValue* args);
LunaValue luna_builtin_pow(int arg_count, LunaValue* args);
LunaValue luna_builtin_sin(int arg_count, LunaValue* args);
LunaValue luna_builtin_cos(int arg_count, LunaValue* args);
LunaValue luna_builtin_tan(int arg_count, LunaValue* args);
LunaValue luna_builtin_random(int arg_count, LunaValue* args);

// String functions
LunaValue luna_builtin_substr(int arg_count, LunaValue* args);
LunaValue luna_builtin_upper(int arg_count, LunaValue* args);
LunaValue luna_builtin_lower(int arg_count, LunaValue* args);
LunaValue luna_builtin_trim(int arg_count, LunaValue* args);
LunaValue luna_builtin_split(int arg_count, LunaValue* args);
LunaValue luna_builtin_join(int arg_count, LunaValue* args);
LunaValue luna_builtin_replace(int arg_count, LunaValue* args);
LunaValue luna_builtin_find(int arg_count, LunaValue* args);
LunaValue luna_builtin_starts_with(int arg_count, LunaValue* args);
LunaValue luna_builtin_ends_with(int arg_count, LunaValue* args);

// Array functions
LunaValue luna_builtin_push(int arg_count, LunaValue* args);
LunaValue luna_builtin_pop(int arg_count, LunaValue* args);
LunaValue luna_builtin_shift(int arg_count, LunaValue* args);
LunaValue luna_builtin_unshift(int arg_count, LunaValue* args);
LunaValue luna_builtin_slice(int arg_count, LunaValue* args);
LunaValue luna_builtin_concat(int arg_count, LunaValue* args);
LunaValue luna_builtin_reverse(int arg_count, LunaValue* args);
LunaValue luna_builtin_sort(int arg_count, LunaValue* args);
LunaValue luna_builtin_map(int arg_count, LunaValue* args);
LunaValue luna_builtin_filter(int arg_count, LunaValue* args);
LunaValue luna_builtin_reduce(int arg_count, LunaValue* args);
LunaValue luna_builtin_foreach(int arg_count, LunaValue* args);

// Table/Object functions
LunaValue luna_builtin_keys(int arg_count, LunaValue* args);
LunaValue luna_builtin_values(int arg_count, LunaValue* args);
LunaValue luna_builtin_has_key(int arg_count, LunaValue* args);
LunaValue luna_builtin_delete_key(int arg_count, LunaValue* args);

// File I/O functions
LunaValue luna_builtin_read_file(int arg_count, LunaValue* args);
LunaValue luna_builtin_write_file(int arg_count, LunaValue* args);
LunaValue luna_builtin_append_file(int arg_count, LunaValue* args);
LunaValue luna_builtin_file_exists(int arg_count, LunaValue* args);

// System functions
LunaValue luna_builtin_exit(int arg_count, LunaValue* args);
LunaValue luna_builtin_system(int arg_count, LunaValue* args);
LunaValue luna_builtin_getenv(int arg_count, LunaValue* args);
LunaValue luna_builtin_setenv(int arg_count, LunaValue* args);

// Error handling
LunaValue luna_builtin_error(int arg_count, LunaValue* args);
LunaValue luna_builtin_assert(int arg_count, LunaValue* args);

// Utility functions
bool luna_check_arg_count(int expected, int actual, const char* function_name);
bool luna_check_arg_type(LunaValue value, LunaValueType expected, const char* function_name, int arg_index);
LunaValue luna_create_error(const char* message);

// Standard library initialization
void luna_init_stdlib(LunaVM* vm);
void luna_register_builtin_function(LunaVM* vm, const char* name, LunaNativeFn function);

// Math constants
extern const double LUNA_PI;
extern const double LUNA_E;

#endif // LUNA_STDLIB_H
