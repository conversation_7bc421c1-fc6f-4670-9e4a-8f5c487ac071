#include "stdlib.h"
#include "value.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <ctype.h>

// Math constants
const double LUNA_PI = 3.14159265358979323846;
const double LUNA_E = 2.71828182845904523536;

// Utility functions
bool luna_check_arg_count(int expected, int actual, const char* function_name) {
    if (actual != expected) {
        fprintf(stderr, "Error: %s() expects %d arguments, got %d\n", function_name, expected, actual);
        return false;
    }
    return true;
}

bool luna_check_arg_type(LunaValue value, LunaValueType expected, const char* function_name, int arg_index) {
    if (value.type != expected) {
        fprintf(stderr, "Error: %s() argument %d has wrong type\n", function_name, arg_index);
        return false;
    }
    return true;
}

LunaValue luna_create_error(const char* message) {
    return LUNA_OBJECT_VAL(luna_copy_string(message, strlen(message)));
}

// Core built-in functions
LunaValue luna_builtin_print(int arg_count, LunaValue* args) {
    for (int i = 0; i < arg_count; i++) {
        luna_print_value(args[i]);
        if (i < arg_count - 1) printf(" ");
    }
    return LUNA_NIL_VAL;
}

LunaValue luna_builtin_println(int arg_count, LunaValue* args) {
    luna_builtin_print(arg_count, args);
    printf("\n");
    return LUNA_NIL_VAL;
}

LunaValue luna_builtin_input(int arg_count, LunaValue* args) {
    if (arg_count > 1) {
        return luna_create_error("input() takes at most 1 argument");
    }
    
    // Print prompt if provided
    if (arg_count == 1) {
        luna_print_value(args[0]);
    }
    
    char buffer[1024];
    if (fgets(buffer, sizeof(buffer), stdin) != NULL) {
        // Remove newline
        buffer[strcspn(buffer, "\n")] = 0;
        return LUNA_OBJECT_VAL(luna_copy_string(buffer, strlen(buffer)));
    }
    
    return LUNA_OBJECT_VAL(luna_copy_string("", 0));
}

LunaValue luna_builtin_clock(int arg_count, LunaValue* args) {
    (void)args; // Unused
    if (!luna_check_arg_count(0, arg_count, "clock")) {
        return LUNA_NIL_VAL;
    }
    return LUNA_NUMBER_VAL((double)clock() / CLOCKS_PER_SEC);
}

LunaValue luna_builtin_type(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "type")) {
        return LUNA_NIL_VAL;
    }
    
    LunaValue value = args[0];
    const char* type_name;
    
    if (LUNA_IS_NIL(value)) {
        type_name = "nil";
    } else if (LUNA_IS_BOOL(value)) {
        type_name = "boolean";
    } else if (LUNA_IS_NUMBER(value)) {
        type_name = "number";
    } else if (LUNA_IS_STRING(value)) {
        type_name = "string";
    } else if (LUNA_IS_FUNCTION(value)) {
        type_name = "function";
    } else if (LUNA_IS_CLASS(value)) {
        type_name = "class";
    } else if (LUNA_IS_INSTANCE(value)) {
        type_name = "instance";
    } else if (LUNA_IS_ARRAY(value)) {
        type_name = "array";
    } else if (LUNA_IS_TABLE(value)) {
        type_name = "table";
    } else {
        type_name = "object";
    }
    
    return LUNA_OBJECT_VAL(luna_copy_string(type_name, strlen(type_name)));
}

LunaValue luna_builtin_len(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "len")) {
        return LUNA_NIL_VAL;
    }
    
    LunaValue value = args[0];
    
    if (LUNA_IS_STRING(value)) {
        return LUNA_NUMBER_VAL(LUNA_AS_STRING(value)->length);
    } else if (LUNA_IS_ARRAY(value)) {
        return LUNA_NUMBER_VAL(LUNA_AS_ARRAY(value)->count);
    } else if (LUNA_IS_TABLE(value)) {
        return LUNA_NUMBER_VAL(LUNA_AS_TABLE(value)->count);
    }
    
    return LUNA_NIL_VAL;
}

LunaValue luna_builtin_str(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "str")) {
        return LUNA_NIL_VAL;
    }
    
    LunaValue value = args[0];
    
    if (LUNA_IS_STRING(value)) {
        return value;
    } else if (LUNA_IS_NUMBER(value)) {
        char buffer[64];
        snprintf(buffer, sizeof(buffer), "%g", LUNA_AS_NUMBER(value));
        return LUNA_OBJECT_VAL(luna_copy_string(buffer, strlen(buffer)));
    } else if (LUNA_IS_BOOL(value)) {
        const char* str = LUNA_AS_BOOL(value) ? "true" : "false";
        return LUNA_OBJECT_VAL(luna_copy_string(str, strlen(str)));
    } else if (LUNA_IS_NIL(value)) {
        return LUNA_OBJECT_VAL(luna_copy_string("nil", 3));
    }
    
    return LUNA_OBJECT_VAL(luna_copy_string("object", 6));
}

LunaValue luna_builtin_num(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "num")) {
        return LUNA_NIL_VAL;
    }
    
    LunaValue value = args[0];
    
    if (LUNA_IS_NUMBER(value)) {
        return value;
    } else if (LUNA_IS_STRING(value)) {
        char* endptr;
        double result = strtod(LUNA_AS_CSTRING(value), &endptr);
        if (*endptr == '\0') {
            return LUNA_NUMBER_VAL(result);
        }
    } else if (LUNA_IS_BOOL(value)) {
        return LUNA_NUMBER_VAL(LUNA_AS_BOOL(value) ? 1.0 : 0.0);
    }
    
    return LUNA_NIL_VAL;
}

LunaValue luna_builtin_bool(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "bool")) {
        return LUNA_NIL_VAL;
    }
    
    LunaValue value = args[0];
    
    if (LUNA_IS_NIL(value)) {
        return LUNA_BOOL_VAL(false);
    } else if (LUNA_IS_BOOL(value)) {
        return value;
    } else if (LUNA_IS_NUMBER(value)) {
        return LUNA_BOOL_VAL(LUNA_AS_NUMBER(value) != 0.0);
    } else if (LUNA_IS_STRING(value)) {
        return LUNA_BOOL_VAL(LUNA_AS_STRING(value)->length > 0);
    }
    
    return LUNA_BOOL_VAL(true);
}

// Math functions
LunaValue luna_builtin_abs(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "abs")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("abs() requires a number");
    }
    
    return LUNA_NUMBER_VAL(fabs(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_min(int arg_count, LunaValue* args) {
    if (arg_count < 1) {
        return luna_create_error("min() requires at least 1 argument");
    }
    
    double min_val = INFINITY;
    for (int i = 0; i < arg_count; i++) {
        if (!LUNA_IS_NUMBER(args[i])) {
            return luna_create_error("min() requires all arguments to be numbers");
        }
        double val = LUNA_AS_NUMBER(args[i]);
        if (val < min_val) {
            min_val = val;
        }
    }
    
    return LUNA_NUMBER_VAL(min_val);
}

LunaValue luna_builtin_max(int arg_count, LunaValue* args) {
    if (arg_count < 1) {
        return luna_create_error("max() requires at least 1 argument");
    }
    
    double max_val = -INFINITY;
    for (int i = 0; i < arg_count; i++) {
        if (!LUNA_IS_NUMBER(args[i])) {
            return luna_create_error("max() requires all arguments to be numbers");
        }
        double val = LUNA_AS_NUMBER(args[i]);
        if (val > max_val) {
            max_val = val;
        }
    }
    
    return LUNA_NUMBER_VAL(max_val);
}

LunaValue luna_builtin_floor(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "floor")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("floor() requires a number");
    }
    
    return LUNA_NUMBER_VAL(floor(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_ceil(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "ceil")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("ceil() requires a number");
    }
    
    return LUNA_NUMBER_VAL(ceil(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_round(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "round")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("round() requires a number");
    }
    
    return LUNA_NUMBER_VAL(round(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_sqrt(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "sqrt")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("sqrt() requires a number");
    }
    
    double val = LUNA_AS_NUMBER(args[0]);
    if (val < 0) {
        return luna_create_error("sqrt() of negative number");
    }
    
    return LUNA_NUMBER_VAL(sqrt(val));
}

LunaValue luna_builtin_pow(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(2, arg_count, "pow")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0]) || !LUNA_IS_NUMBER(args[1])) {
        return luna_create_error("pow() requires two numbers");
    }
    
    return LUNA_NUMBER_VAL(pow(LUNA_AS_NUMBER(args[0]), LUNA_AS_NUMBER(args[1])));
}

LunaValue luna_builtin_sin(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "sin")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("sin() requires a number");
    }
    
    return LUNA_NUMBER_VAL(sin(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_cos(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "cos")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("cos() requires a number");
    }
    
    return LUNA_NUMBER_VAL(cos(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_tan(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "tan")) {
        return LUNA_NIL_VAL;
    }
    
    if (!LUNA_IS_NUMBER(args[0])) {
        return luna_create_error("tan() requires a number");
    }
    
    return LUNA_NUMBER_VAL(tan(LUNA_AS_NUMBER(args[0])));
}

LunaValue luna_builtin_random(int arg_count, LunaValue* args) {
    static bool seeded = false;
    if (!seeded) {
        srand((unsigned int)time(NULL));
        seeded = true;
    }
    
    if (arg_count == 0) {
        return LUNA_NUMBER_VAL((double)rand() / RAND_MAX);
    } else if (arg_count == 1) {
        if (!LUNA_IS_NUMBER(args[0])) {
            return luna_create_error("random() requires a number");
        }
        int max = (int)LUNA_AS_NUMBER(args[0]);
        return LUNA_NUMBER_VAL(rand() % max);
    } else if (arg_count == 2) {
        if (!LUNA_IS_NUMBER(args[0]) || !LUNA_IS_NUMBER(args[1])) {
            return luna_create_error("random() requires numbers");
        }
        int min = (int)LUNA_AS_NUMBER(args[0]);
        int max = (int)LUNA_AS_NUMBER(args[1]);
        return LUNA_NUMBER_VAL(min + rand() % (max - min + 1));
    }
    
    return luna_create_error("random() takes 0, 1, or 2 arguments");
}

// String functions
LunaValue luna_builtin_substr(int arg_count, LunaValue* args) {
    if (arg_count < 2 || arg_count > 3) {
        return luna_create_error("substr() takes 2 or 3 arguments");
    }

    if (!LUNA_IS_STRING(args[0]) || !LUNA_IS_NUMBER(args[1])) {
        return luna_create_error("substr() requires string and number arguments");
    }

    LunaString* str = LUNA_AS_STRING(args[0]);
    int start = (int)LUNA_AS_NUMBER(args[1]);
    int length = str->length - start;

    if (arg_count == 3) {
        if (!LUNA_IS_NUMBER(args[2])) {
            return luna_create_error("substr() length must be a number");
        }
        length = (int)LUNA_AS_NUMBER(args[2]);
    }

    if (start < 0 || start >= str->length || length < 0) {
        return LUNA_OBJECT_VAL(luna_copy_string("", 0));
    }

    if (start + length > str->length) {
        length = str->length - start;
    }

    return LUNA_OBJECT_VAL(luna_copy_string(str->chars + start, length));
}

LunaValue luna_builtin_upper(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "upper")) {
        return LUNA_NIL_VAL;
    }

    if (!LUNA_IS_STRING(args[0])) {
        return luna_create_error("upper() requires a string");
    }

    LunaString* str = LUNA_AS_STRING(args[0]);
    char* upper_chars = malloc(str->length + 1);

    for (int i = 0; i < str->length; i++) {
        upper_chars[i] = toupper(str->chars[i]);
    }
    upper_chars[str->length] = '\0';

    return LUNA_OBJECT_VAL(luna_take_string(upper_chars, str->length));
}

LunaValue luna_builtin_lower(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "lower")) {
        return LUNA_NIL_VAL;
    }

    if (!LUNA_IS_STRING(args[0])) {
        return luna_create_error("lower() requires a string");
    }

    LunaString* str = LUNA_AS_STRING(args[0]);
    char* lower_chars = malloc(str->length + 1);

    for (int i = 0; i < str->length; i++) {
        lower_chars[i] = tolower(str->chars[i]);
    }
    lower_chars[str->length] = '\0';

    return LUNA_OBJECT_VAL(luna_take_string(lower_chars, str->length));
}

LunaValue luna_builtin_trim(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "trim")) {
        return LUNA_NIL_VAL;
    }

    if (!LUNA_IS_STRING(args[0])) {
        return luna_create_error("trim() requires a string");
    }

    LunaString* str = LUNA_AS_STRING(args[0]);
    const char* start = str->chars;
    const char* end = str->chars + str->length - 1;

    // Trim leading whitespace
    while (start <= end && isspace(*start)) {
        start++;
    }

    // Trim trailing whitespace
    while (end >= start && isspace(*end)) {
        end--;
    }

    int length = end - start + 1;
    if (length <= 0) {
        return LUNA_OBJECT_VAL(luna_copy_string("", 0));
    }

    return LUNA_OBJECT_VAL(luna_copy_string(start, length));
}

// Array functions
LunaValue luna_builtin_push(int arg_count, LunaValue* args) {
    if (arg_count < 2) {
        return luna_create_error("push() requires at least 2 arguments");
    }

    if (!LUNA_IS_ARRAY(args[0])) {
        return luna_create_error("push() requires an array as first argument");
    }

    LunaArray* array = LUNA_AS_ARRAY(args[0]);

    for (int i = 1; i < arg_count; i++) {
        luna_array_push(array, args[i]);
    }

    return LUNA_NUMBER_VAL(array->count);
}

LunaValue luna_builtin_pop(int arg_count, LunaValue* args) {
    if (!luna_check_arg_count(1, arg_count, "pop")) {
        return LUNA_NIL_VAL;
    }

    if (!LUNA_IS_ARRAY(args[0])) {
        return luna_create_error("pop() requires an array");
    }

    LunaArray* array = LUNA_AS_ARRAY(args[0]);
    return luna_array_pop(array);
}

// System functions
LunaValue luna_builtin_exit(int arg_count, LunaValue* args) {
    int code = 0;

    if (arg_count > 1) {
        return luna_create_error("exit() takes at most 1 argument");
    }

    if (arg_count == 1) {
        if (!LUNA_IS_NUMBER(args[0])) {
            return luna_create_error("exit() code must be a number");
        }
        code = (int)LUNA_AS_NUMBER(args[0]);
    }

    exit(code);
    return LUNA_NIL_VAL; // Never reached
}

LunaValue luna_builtin_assert(int arg_count, LunaValue* args) {
    if (arg_count < 1 || arg_count > 2) {
        return luna_create_error("assert() takes 1 or 2 arguments");
    }

    bool condition = true;
    if (LUNA_IS_NIL(args[0])) {
        condition = false;
    } else if (LUNA_IS_BOOL(args[0])) {
        condition = LUNA_AS_BOOL(args[0]);
    }

    if (!condition) {
        const char* message = "Assertion failed";
        if (arg_count == 2 && LUNA_IS_STRING(args[1])) {
            message = LUNA_AS_CSTRING(args[1]);
        }

        fprintf(stderr, "Assertion Error: %s\n", message);
        exit(1);
    }

    return LUNA_NIL_VAL;
}

// Standard library initialization
void luna_register_builtin_function(LunaVM* vm, const char* name, LunaNativeFn function) {
    luna_push(vm, LUNA_OBJECT_VAL(luna_copy_string(name, strlen(name))));
    luna_push(vm, LUNA_OBJECT_VAL(luna_new_native_function(function, LUNA_AS_STRING(vm->stack[0]))));
    luna_table_set(vm->globals, LUNA_AS_STRING(vm->stack[0]), vm->stack[1]);
    luna_pop(vm);
    luna_pop(vm);
}

void luna_init_stdlib(LunaVM* vm) {
    // Core functions
    luna_register_builtin_function(vm, "print", luna_builtin_print);
    luna_register_builtin_function(vm, "println", luna_builtin_println);
    luna_register_builtin_function(vm, "input", luna_builtin_input);
    luna_register_builtin_function(vm, "clock", luna_builtin_clock);
    luna_register_builtin_function(vm, "type", luna_builtin_type);
    luna_register_builtin_function(vm, "len", luna_builtin_len);
    luna_register_builtin_function(vm, "str", luna_builtin_str);
    luna_register_builtin_function(vm, "num", luna_builtin_num);
    luna_register_builtin_function(vm, "bool", luna_builtin_bool);

    // Math functions
    luna_register_builtin_function(vm, "abs", luna_builtin_abs);
    luna_register_builtin_function(vm, "min", luna_builtin_min);
    luna_register_builtin_function(vm, "max", luna_builtin_max);
    luna_register_builtin_function(vm, "floor", luna_builtin_floor);
    luna_register_builtin_function(vm, "ceil", luna_builtin_ceil);
    luna_register_builtin_function(vm, "round", luna_builtin_round);
    luna_register_builtin_function(vm, "sqrt", luna_builtin_sqrt);
    luna_register_builtin_function(vm, "pow", luna_builtin_pow);
    luna_register_builtin_function(vm, "sin", luna_builtin_sin);
    luna_register_builtin_function(vm, "cos", luna_builtin_cos);
    luna_register_builtin_function(vm, "tan", luna_builtin_tan);
    luna_register_builtin_function(vm, "random", luna_builtin_random);

    // String functions
    luna_register_builtin_function(vm, "substr", luna_builtin_substr);
    luna_register_builtin_function(vm, "upper", luna_builtin_upper);
    luna_register_builtin_function(vm, "lower", luna_builtin_lower);
    luna_register_builtin_function(vm, "trim", luna_builtin_trim);

    // Array functions
    luna_register_builtin_function(vm, "push", luna_builtin_push);
    luna_register_builtin_function(vm, "pop", luna_builtin_pop);

    // System functions
    luna_register_builtin_function(vm, "exit", luna_builtin_exit);
    luna_register_builtin_function(vm, "assert", luna_builtin_assert);

    // Math constants
    luna_define_global(vm, "PI", LUNA_NUMBER_VAL(LUNA_PI));
    luna_define_global(vm, "E", LUNA_NUMBER_VAL(LUNA_E));
}
