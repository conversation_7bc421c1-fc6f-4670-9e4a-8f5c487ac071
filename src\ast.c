#include "ast.h"
#include <stdlib.h>
#include <stdio.h>

LunaASTNode* luna_create_ast_node(LunaASTNodeType type, int line, int column) {
    LunaASTNode* node = malloc(sizeof(LunaASTNode));
    if (node == NULL) {
        fprintf(stderr, "Memory allocation failed for AST node\n");
        exit(1);
    }
    
    node->type = type;
    node->line = line;
    node->column = column;
    
    return node;
}

LunaASTNodeList* luna_create_ast_node_list(void) {
    LunaASTNodeList* list = malloc(sizeof(LunaASTNodeList));
    if (list == NULL) {
        fprintf(stderr, "Memory allocation failed for AST node list\n");
        exit(1);
    }
    
    list->nodes = NULL;
    list->count = 0;
    list->capacity = 0;
    
    return list;
}

void luna_ast_node_list_append(LunaASTNodeList* list, LunaASTNode* node) {
    if (list->capacity < list->count + 1) {
        int old_capacity = list->capacity;
        list->capacity = old_capacity < 8 ? 8 : old_capacity * 2;
        list->nodes = realloc(list->nodes, sizeof(LunaASTNode*) * list->capacity);
        
        if (list->nodes == NULL) {
            fprintf(stderr, "Memory allocation failed for AST node list\n");
            exit(1);
        }
    }
    
    list->nodes[list->count] = node;
    list->count++;
}

void luna_free_ast_node_list(LunaASTNodeList* list) {
    if (list == NULL) return;
    
    for (int i = 0; i < list->count; i++) {
        luna_free_ast_node(list->nodes[i]);
    }
    
    free(list->nodes);
    free(list);
}

void luna_free_ast_node(LunaASTNode* node) {
    if (node == NULL) return;
    
    switch (node->type) {
        case AST_PROGRAM:
            luna_free_ast_node_list(node->program.statements);
            break;
        case AST_BLOCK:
            luna_free_ast_node_list(node->block.statements);
            break;
        case AST_EXPRESSION_STMT:
            luna_free_ast_node(node->expression_stmt.expression);
            break;
        case AST_VAR_DECLARATION:
            luna_free_ast_node(node->var_declaration.initializer);
            break;
        case AST_FUNC_DECLARATION:
            luna_free_ast_node_list(node->func_declaration.params);
            luna_free_ast_node(node->func_declaration.body);
            break;
        case AST_CLASS_DECLARATION:
            luna_free_ast_node(node->class_declaration.superclass);
            luna_free_ast_node_list(node->class_declaration.methods);
            break;
        case AST_IF_STMT:
            luna_free_ast_node(node->if_stmt.condition);
            luna_free_ast_node(node->if_stmt.then_branch);
            luna_free_ast_node(node->if_stmt.else_branch);
            break;
        case AST_WHILE_STMT:
            luna_free_ast_node(node->while_stmt.condition);
            luna_free_ast_node(node->while_stmt.body);
            break;
        case AST_FOR_STMT:
            luna_free_ast_node(node->for_stmt.initializer);
            luna_free_ast_node(node->for_stmt.condition);
            luna_free_ast_node(node->for_stmt.increment);
            luna_free_ast_node(node->for_stmt.body);
            break;
        case AST_SWITCH_STMT:
            luna_free_ast_node(node->switch_stmt.expression);
            luna_free_ast_node_list(node->switch_stmt.cases);
            luna_free_ast_node(node->switch_stmt.default_case);
            break;
        case AST_RETURN_STMT:
            luna_free_ast_node(node->return_stmt.expression);
            break;
        case AST_TRY_STMT:
            luna_free_ast_node(node->try_stmt.try_block);
            luna_free_ast_node(node->try_stmt.catch_block);
            luna_free_ast_node(node->try_stmt.finally_block);
            break;
        case AST_THROW_STMT:
            luna_free_ast_node(node->throw_stmt.expression);
            break;
        case AST_BINARY_EXPR:
            luna_free_ast_node(node->binary.left);
            luna_free_ast_node(node->binary.right);
            break;
        case AST_UNARY_EXPR:
            luna_free_ast_node(node->unary.right);
            break;
        case AST_CALL_EXPR:
            luna_free_ast_node(node->call.callee);
            luna_free_ast_node_list(node->call.arguments);
            break;
        case AST_GET_EXPR:
            luna_free_ast_node(node->get.object);
            break;
        case AST_SET_EXPR:
            luna_free_ast_node(node->set.object);
            luna_free_ast_node(node->set.value);
            break;
        case AST_ARRAY_EXPR:
            luna_free_ast_node_list(node->array.elements);
            break;
        case AST_INDEX_EXPR:
            luna_free_ast_node(node->index.array);
            luna_free_ast_node(node->index.index);
            break;
        case AST_ASSIGN_EXPR:
            luna_free_ast_node(node->assign.value);
            break;
        case AST_LOGICAL_EXPR:
            luna_free_ast_node(node->logical.left);
            luna_free_ast_node(node->logical.right);
            break;
        case AST_CONDITIONAL_EXPR:
            luna_free_ast_node(node->conditional.condition);
            luna_free_ast_node(node->conditional.then_expr);
            luna_free_ast_node(node->conditional.else_expr);
            break;
        case AST_BREAK_STMT:
        case AST_CONTINUE_STMT:
        case AST_LITERAL_EXPR:
        case AST_VARIABLE_EXPR:
        case AST_THIS_EXPR:
        case AST_SUPER_EXPR:
            // No additional cleanup needed
            break;
    }
    
    free(node);
}

// AST node creation helpers
LunaASTNode* luna_create_program(LunaASTNodeList* statements) {
    LunaASTNode* node = luna_create_ast_node(AST_PROGRAM, 0, 0);
    node->program.statements = statements;
    return node;
}

LunaASTNode* luna_create_block(LunaASTNodeList* statements) {
    LunaASTNode* node = luna_create_ast_node(AST_BLOCK, 0, 0);
    node->block.statements = statements;
    return node;
}

LunaASTNode* luna_create_expression_stmt(LunaASTNode* expression) {
    LunaASTNode* node = luna_create_ast_node(AST_EXPRESSION_STMT, expression->line, expression->column);
    node->expression_stmt.expression = expression;
    return node;
}

LunaASTNode* luna_create_var_declaration(LunaToken name, LunaASTNode* initializer, bool is_const) {
    LunaASTNode* node = luna_create_ast_node(AST_VAR_DECLARATION, name.line, name.column);
    node->var_declaration.name = name;
    node->var_declaration.initializer = initializer;
    node->var_declaration.is_const = is_const;
    return node;
}

LunaASTNode* luna_create_func_declaration(LunaToken name, LunaASTNodeList* params, LunaASTNode* body) {
    LunaASTNode* node = luna_create_ast_node(AST_FUNC_DECLARATION, name.line, name.column);
    node->func_declaration.name = name;
    node->func_declaration.params = params;
    node->func_declaration.body = body;
    return node;
}

LunaASTNode* luna_create_class_declaration(LunaToken name, LunaASTNode* superclass, LunaASTNodeList* methods) {
    LunaASTNode* node = luna_create_ast_node(AST_CLASS_DECLARATION, name.line, name.column);
    node->class_declaration.name = name;
    node->class_declaration.superclass = superclass;
    node->class_declaration.methods = methods;
    return node;
}

LunaASTNode* luna_create_if_stmt(LunaASTNode* condition, LunaASTNode* then_branch, LunaASTNode* else_branch) {
    LunaASTNode* node = luna_create_ast_node(AST_IF_STMT, condition->line, condition->column);
    node->if_stmt.condition = condition;
    node->if_stmt.then_branch = then_branch;
    node->if_stmt.else_branch = else_branch;
    return node;
}

LunaASTNode* luna_create_while_stmt(LunaASTNode* condition, LunaASTNode* body) {
    LunaASTNode* node = luna_create_ast_node(AST_WHILE_STMT, condition->line, condition->column);
    node->while_stmt.condition = condition;
    node->while_stmt.body = body;
    return node;
}

LunaASTNode* luna_create_for_stmt(LunaASTNode* initializer, LunaASTNode* condition, LunaASTNode* increment, LunaASTNode* body) {
    LunaASTNode* node = luna_create_ast_node(AST_FOR_STMT, 0, 0);
    if (initializer) {
        node->line = initializer->line;
        node->column = initializer->column;
    } else if (condition) {
        node->line = condition->line;
        node->column = condition->column;
    }
    node->for_stmt.initializer = initializer;
    node->for_stmt.condition = condition;
    node->for_stmt.increment = increment;
    node->for_stmt.body = body;
    return node;
}

LunaASTNode* luna_create_return_stmt(LunaASTNode* expression) {
    LunaASTNode* node = luna_create_ast_node(AST_RETURN_STMT, 0, 0);
    if (expression) {
        node->line = expression->line;
        node->column = expression->column;
    }
    node->return_stmt.expression = expression;
    return node;
}

LunaASTNode* luna_create_break_stmt(void) {
    return luna_create_ast_node(AST_BREAK_STMT, 0, 0);
}

LunaASTNode* luna_create_continue_stmt(void) {
    return luna_create_ast_node(AST_CONTINUE_STMT, 0, 0);
}

LunaASTNode* luna_create_binary_expr(LunaASTNode* left, LunaToken operator, LunaASTNode* right) {
    LunaASTNode* node = luna_create_ast_node(AST_BINARY_EXPR, operator.line, operator.column);
    node->binary.left = left;
    node->binary.operator = operator;
    node->binary.right = right;
    return node;
}

LunaASTNode* luna_create_unary_expr(LunaToken operator, LunaASTNode* right) {
    LunaASTNode* node = luna_create_ast_node(AST_UNARY_EXPR, operator.line, operator.column);
    node->unary.operator = operator;
    node->unary.right = right;
    return node;
}

LunaASTNode* luna_create_call_expr(LunaASTNode* callee, LunaASTNodeList* arguments) {
    LunaASTNode* node = luna_create_ast_node(AST_CALL_EXPR, callee->line, callee->column);
    node->call.callee = callee;
    node->call.arguments = arguments;
    return node;
}

LunaASTNode* luna_create_literal_expr(LunaValue value) {
    LunaASTNode* node = luna_create_ast_node(AST_LITERAL_EXPR, 0, 0);
    node->literal.value = value;
    return node;
}

LunaASTNode* luna_create_variable_expr(LunaToken name) {
    LunaASTNode* node = luna_create_ast_node(AST_VARIABLE_EXPR, name.line, name.column);
    node->variable.name = name;
    return node;
}

LunaASTNode* luna_create_assign_expr(LunaToken name, LunaASTNode* value) {
    LunaASTNode* node = luna_create_ast_node(AST_ASSIGN_EXPR, name.line, name.column);
    node->assign.name = name;
    node->assign.value = value;
    return node;
}
