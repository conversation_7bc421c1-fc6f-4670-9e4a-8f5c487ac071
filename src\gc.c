#include "gc.h"
#include "vm.h"
#include "value.h"
#include <stdio.h>
#include <stdlib.h>

#ifdef DEBUG_LOG_GC
#include <stdio.h>
#define GC_LOG(...) printf(__VA_ARGS__)
#else
#define GC_LOG(...)
#endif

void* luna_reallocate(void* pointer, size_t old_size, size_t new_size) {
    if (new_size == 0) {
        free(pointer);
        return NULL;
    }
    
    void* result = realloc(pointer, new_size);
    if (result == NULL) {
        fprintf(stderr, "Memory allocation failed\n");
        exit(1);
    }
    
    return result;
}

void luna_mark_object(LunaObject* object) {
    if (object == NULL) return;
    if (object->is_marked) return;
    
    GC_LOG("%p mark ", (void*)object);
    
    object->is_marked = true;
    
    // Add to gray stack for processing
    extern LunaVM* current_vm;
    if (current_vm != NULL) {
        if (current_vm->gray_capacity < current_vm->gray_count + 1) {
            current_vm->gray_capacity = current_vm->gray_capacity < 8 ? 8 : current_vm->gray_capacity * 2;
            current_vm->gray_stack = realloc(current_vm->gray_stack, 
                                           sizeof(LunaObject*) * current_vm->gray_capacity);
            
            if (current_vm->gray_stack == NULL) {
                fprintf(stderr, "Failed to allocate gray stack\n");
                exit(1);
            }
        }
        
        current_vm->gray_stack[current_vm->gray_count++] = object;
    }
}

void luna_mark_value(LunaValue value) {
    if (LUNA_IS_OBJECT(value)) {
        luna_mark_object(LUNA_AS_OBJECT(value));
    }
}

void luna_mark_array(LunaValueArray* array) {
    for (int i = 0; i < array->count; i++) {
        luna_mark_value(array->values[i]);
    }
}

void luna_mark_table(LunaTable* table) {
    for (int i = 0; i < table->capacity; i++) {
        LunaEntry* entry = &table->entries[i];
        if (entry->key != NULL) {
            luna_mark_object((LunaObject*)entry->key);
            luna_mark_value(entry->value);
        }
    }
}

static void luna_blacken_object(LunaObject* object) {
    GC_LOG("%p blacken ", (void*)object);
    
    switch (object->type) {
        case LUNA_OBJ_STRING:
            // Strings have no references
            break;
            
        case LUNA_OBJ_FUNCTION: {
            LunaFunction* function = (LunaFunction*)object;
            luna_mark_object((LunaObject*)function->name);
            luna_mark_array(&function->chunk->constants);
            break;
        }
        
        case LUNA_OBJ_NATIVE_FUNCTION: {
            LunaNativeFunction* native = (LunaNativeFunction*)object;
            luna_mark_object((LunaObject*)native->name);
            break;
        }
        
        case LUNA_OBJ_CLOSURE: {
            LunaClosure* closure = (LunaClosure*)object;
            luna_mark_object((LunaObject*)closure->function);
            for (int i = 0; i < closure->upvalue_count; i++) {
                luna_mark_object((LunaObject*)closure->upvalues[i]);
            }
            break;
        }
        
        case LUNA_OBJ_UPVALUE: {
            LunaUpvalue* upvalue = (LunaUpvalue*)object;
            luna_mark_value(upvalue->closed);
            break;
        }
        
        case LUNA_OBJ_CLASS: {
            LunaClass* klass = (LunaClass*)object;
            luna_mark_object((LunaObject*)klass->name);
            luna_mark_table(klass->methods);
            break;
        }
        
        case LUNA_OBJ_INSTANCE: {
            LunaInstance* instance = (LunaInstance*)object;
            luna_mark_object((LunaObject*)instance->klass);
            luna_mark_table(instance->fields);
            break;
        }
        
        case LUNA_OBJ_BOUND_METHOD: {
            LunaBoundMethod* bound = (LunaBoundMethod*)object;
            luna_mark_value(bound->receiver);
            luna_mark_object((LunaObject*)bound->method);
            break;
        }
        
        case LUNA_OBJ_ARRAY: {
            LunaArray* array = (LunaArray*)object;
            for (int i = 0; i < array->count; i++) {
                luna_mark_value(array->elements[i]);
            }
            break;
        }
        
        case LUNA_OBJ_TABLE: {
            LunaTable* table = (LunaTable*)object;
            luna_mark_table(table);
            break;
        }
    }
}

void luna_mark_roots(LunaVM* vm) {
    // Mark stack values
    for (LunaValue* slot = vm->stack; slot < vm->stack_top; slot++) {
        luna_mark_value(*slot);
    }
    
    // Mark call frames
    for (int i = 0; i < vm->frame_count; i++) {
        luna_mark_object((LunaObject*)vm->frames[i].closure);
    }
    
    // Mark open upvalues
    for (LunaUpvalue* upvalue = vm->open_upvalues; upvalue != NULL; upvalue = upvalue->next) {
        luna_mark_object((LunaObject*)upvalue);
    }
    
    // Mark globals
    luna_mark_table(vm->globals);
    
    // Mark interned strings
    luna_mark_table(vm->strings);
    
    // Mark init string
    luna_mark_object((LunaObject*)vm->init_string);
    
    // Mark current exception
    if (vm->has_exception) {
        luna_mark_value(vm->current_exception);
    }
    
    // Mark compiled functions (JIT)
    if (vm->compiled_functions != NULL) {
        luna_mark_table(vm->compiled_functions);
    }
}

void luna_trace_references(LunaVM* vm) {
    while (vm->gray_count > 0) {
        LunaObject* object = vm->gray_stack[--vm->gray_count];
        luna_blacken_object(object);
    }
}

static void luna_table_remove_white(LunaTable* table) {
    for (int i = 0; i < table->capacity; i++) {
        LunaEntry* entry = &table->entries[i];
        if (entry->key != NULL && !entry->key->obj.is_marked) {
            luna_table_delete(table, entry->key);
        }
    }
}

void luna_sweep(LunaVM* vm) {
    LunaObject* previous = NULL;
    LunaObject* object = vm->objects;
    
    while (object != NULL) {
        if (object->is_marked) {
            // Reset mark for next collection
            object->is_marked = false;
            previous = object;
            object = object->next;
        } else {
            // Remove unmarked object
            LunaObject* unreached = object;
            object = object->next;
            
            if (previous != NULL) {
                previous->next = object;
            } else {
                vm->objects = object;
            }
            
            luna_free_object(unreached);
        }
    }
    
    // Remove white strings from intern table
    luna_table_remove_white(vm->strings);
}

void luna_free_object(LunaObject* object) {
    GC_LOG("%p free type %d\n", (void*)object, object->type);
    
    switch (object->type) {
        case LUNA_OBJ_STRING: {
            LunaString* string = (LunaString*)object;
            free(string->chars);
            free(string);
            break;
        }
        
        case LUNA_OBJ_FUNCTION: {
            LunaFunction* function = (LunaFunction*)object;
            luna_free_chunk(function->chunk);
            free(function->chunk);
            free(function);
            break;
        }
        
        case LUNA_OBJ_NATIVE_FUNCTION: {
            free(object);
            break;
        }
        
        case LUNA_OBJ_CLOSURE: {
            LunaClosure* closure = (LunaClosure*)object;
            free(closure->upvalues);
            free(closure);
            break;
        }
        
        case LUNA_OBJ_UPVALUE: {
            free(object);
            break;
        }
        
        case LUNA_OBJ_CLASS: {
            LunaClass* klass = (LunaClass*)object;
            // Note: methods table will be freed by GC
            free(klass);
            break;
        }
        
        case LUNA_OBJ_INSTANCE: {
            LunaInstance* instance = (LunaInstance*)object;
            // Note: fields table will be freed by GC
            free(instance);
            break;
        }
        
        case LUNA_OBJ_BOUND_METHOD: {
            free(object);
            break;
        }
        
        case LUNA_OBJ_ARRAY: {
            LunaArray* array = (LunaArray*)object;
            free(array->elements);
            free(array);
            break;
        }
        
        case LUNA_OBJ_TABLE: {
            LunaTable* table = (LunaTable*)object;
            free(table->entries);
            free(table);
            break;
        }
    }
}

void luna_collect_garbage(LunaVM* vm) {
    GC_LOG("-- gc begin\n");
    
    size_t before = vm->bytes_allocated;
    
    luna_mark_roots(vm);
    luna_trace_references(vm);
    luna_sweep(vm);
    
    vm->next_gc = vm->bytes_allocated * LUNA_GC_GROWTH_FACTOR;
    
    GC_LOG("-- gc end\n");
    GC_LOG("   collected %zu bytes (from %zu to %zu) next at %zu\n",
           before - vm->bytes_allocated, before, vm->bytes_allocated, vm->next_gc);
}

void luna_free_objects(LunaVM* vm) {
    LunaObject* object = vm->objects;
    while (object != NULL) {
        LunaObject* next = object->next;
        luna_free_object(object);
        object = next;
    }
    
    free(vm->gray_stack);
}

void luna_set_gc_threshold(LunaVM* vm, size_t threshold) {
    vm->next_gc = threshold;
}

size_t luna_get_bytes_allocated(LunaVM* vm) {
    return vm->bytes_allocated;
}
