#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define LUNA_VERSION "1.0.0"

static void print_version(void) {
    printf("luna %s\n", LUNA_VERSION);
}

static void print_help(void) {
    printf("Luna %s\n", LUNA_VERSION);
    printf("A high-level programming language\n\n");
    printf("USAGE:\n");
    printf("    luna [OPTIONS] [FILE]\n\n");
    printf("ARGS:\n");
    printf("    <FILE>    Luna source file to execute (.luna)\n\n");
    printf("OPTIONS:\n");
    printf("    -h, --help       Print help information\n");
    printf("    -V, --version    Print version information\n");
    printf("    -v, --verbose    Use verbose output\n");
    printf("    --repl           Start interactive REPL\n\n");
    printf("EXAMPLES:\n");
    printf("    luna                    Start interactive REPL\n");
    printf("    luna script.luna        Run a Luna script\n");
    printf("    luna --help             Show this help message\n");
    printf("    luna --version          Show version information\n");
}

static void print_intro(void) {
    printf("Welcome to Luna!\n\n");
    printf("Luna is a high-level, dynamically-typed programming language designed\n");
    printf("for simplicity and expressiveness. It features:\n\n");
    printf("  • Dynamic typing with automatic memory management\n");
    printf("  • First-class functions and closures\n");
    printf("  • Built-in data structures (arrays, strings)\n");
    printf("  • Rich standard library\n");
    printf("  • Interactive REPL for development\n\n");
    printf("Get started by typing some Luna code or 'exit' to quit.\n");
    printf("For help with syntax, visit: https://luna-lang.org/docs\n\n");
}

static void luna_repl(int verbose) {
    char line[1024];
    
    print_intro();
    
    for (;;) {
        printf("luna> ");
        
        if (!fgets(line, sizeof(line), stdin)) {
            printf("\n");
            break;
        }
        
        // Remove newline
        line[strcspn(line, "\n")] = 0;
        
        // Check for exit command
        if (strcmp(line, "exit") == 0 || strcmp(line, "quit") == 0) {
            break;
        }
        
        // Check for help command
        if (strcmp(line, "help") == 0) {
            printf("Luna REPL Commands:\n");
            printf("  help    - Show this help\n");
            printf("  exit    - Exit the REPL\n");
            printf("  quit    - Exit the REPL\n");
            printf("  clear   - Clear the screen\n\n");
            printf("Enter any Luna expression or statement to evaluate it.\n");
            continue;
        }
        
        // Check for clear command
        if (strcmp(line, "clear") == 0) {
            #ifdef _WIN32
                system("cls");
            #else
                system("clear");
            #endif
            continue;
        }
        
        // Skip empty lines
        if (strlen(line) == 0) {
            continue;
        }
        
        if (verbose) {
            printf("Input: %s\n", line);
        }
        
        // For now, just echo back (placeholder for actual interpreter)
        printf("Echo: %s\n", line);
        printf("(Luna interpreter not yet implemented)\n");
    }
    
    printf("Goodbye!\n");
}

static int is_luna_file(const char* path) {
    size_t len = strlen(path);
    return len > 5 && strcmp(path + len - 5, ".luna") == 0;
}

static void luna_run_file(const char* path, int verbose) {
    if (!is_luna_file(path)) {
        fprintf(stderr, "error: file must have .luna extension\n");
        exit(1);
    }
    
    if (verbose) {
        printf("Reading file: %s\n", path);
    }
    
    FILE* file = fopen(path, "r");
    if (!file) {
        fprintf(stderr, "error: could not open file '%s'\n", path);
        exit(1);
    }
    
    printf("Running Luna script: %s\n", path);
    printf("(Luna interpreter not yet implemented)\n");
    
    fclose(file);
}

int main(int argc, const char* argv[]) {
    int verbose = 0;
    int show_help = 0;
    int show_version = 0;
    int force_repl = 0;
    const char* filename = NULL;
    
    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            show_help = 1;
        } else if (strcmp(argv[i], "-V") == 0 || strcmp(argv[i], "--version") == 0) {
            show_version = 1;
        } else if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--verbose") == 0) {
            verbose = 1;
        } else if (strcmp(argv[i], "--repl") == 0) {
            force_repl = 1;
        } else if (argv[i][0] == '-') {
            fprintf(stderr, "error: unknown option '%s'\n", argv[i]);
            fprintf(stderr, "Try 'luna --help' for more information.\n");
            exit(1);
        } else {
            if (filename != NULL) {
                fprintf(stderr, "error: multiple input files specified\n");
                exit(1);
            }
            filename = argv[i];
        }
    }
    
    // Handle options
    if (show_help) {
        print_help();
        return 0;
    }
    
    if (show_version) {
        print_version();
        return 0;
    }
    
    // Execute based on arguments
    if (force_repl || filename == NULL) {
        luna_repl(verbose);
    } else {
        luna_run_file(filename, verbose);
    }
    
    return 0;
}
