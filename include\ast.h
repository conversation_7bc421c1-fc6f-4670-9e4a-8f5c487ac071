#ifndef LUNA_AST_H
#define LUNA_AST_H

#include "luna.h"
#include "value.h"

// Forward declarations
typedef struct LunaASTNode LunaASTNode;
typedef struct LunaASTNodeList LunaASTNodeList;

// AST node list for storing multiple nodes
struct LunaASTNodeList {
    LunaASTNode** nodes;
    int count;
    int capacity;
};

// Base AST node structure
struct LunaASTNode {
    LunaASTNodeType type;
    int line;
    int column;
    
    union {
        // Statements
        struct {
            LunaASTNodeList* statements;
        } program;
        
        struct {
            LunaASTNodeList* statements;
        } block;
        
        struct {
            LunaASTNode* expression;
        } expression_stmt;
        
        struct {
            LunaToken name;
            LunaASTNode* initializer;
            bool is_const;
        } var_declaration;
        
        struct {
            LunaToken name;
            LunaASTNodeList* params;
            LunaASTNode* body;
        } func_declaration;
        
        struct {
            LunaToken name;
            LunaASTNode* superclass;
            LunaASTNodeList* methods;
        } class_declaration;
        
        struct {
            LunaASTNode* condition;
            LunaASTNode* then_branch;
            LunaASTNode* else_branch;
        } if_stmt;
        
        struct {
            LunaASTNode* condition;
            LunaASTNode* body;
        } while_stmt;
        
        struct {
            LunaASTNode* initializer;
            LunaASTNode* condition;
            LunaASTNode* increment;
            LunaASTNode* body;
        } for_stmt;
        
        struct {
            LunaASTNode* expression;
            LunaASTNodeList* cases;
            LunaASTNode* default_case;
        } switch_stmt;
        
        struct {
            LunaASTNode* expression;
        } return_stmt;
        
        struct {
            // No additional data needed
        } break_stmt;
        
        struct {
            // No additional data needed
        } continue_stmt;
        
        struct {
            LunaASTNode* try_block;
            LunaToken catch_var;
            LunaASTNode* catch_block;
            LunaASTNode* finally_block;
        } try_stmt;
        
        struct {
            LunaASTNode* expression;
        } throw_stmt;
        
        // Expressions
        struct {
            LunaASTNode* left;
            LunaToken operator;
            LunaASTNode* right;
        } binary;
        
        struct {
            LunaToken operator;
            LunaASTNode* right;
        } unary;
        
        struct {
            LunaASTNode* callee;
            LunaASTNodeList* arguments;
        } call;
        
        struct {
            LunaASTNode* object;
            LunaToken name;
        } get;
        
        struct {
            LunaASTNode* object;
            LunaToken name;
            LunaASTNode* value;
        } set;
        
        struct {
            LunaASTNodeList* elements;
        } array;
        
        struct {
            LunaASTNode* array;
            LunaASTNode* index;
        } index;
        
        struct {
            LunaToken name;
            LunaASTNode* value;
        } assign;
        
        struct {
            LunaASTNode* left;
            LunaToken operator;
            LunaASTNode* right;
        } logical;
        
        struct {
            LunaASTNode* condition;
            LunaASTNode* then_expr;
            LunaASTNode* else_expr;
        } conditional;
        
        struct {
            LunaValue value;
        } literal;
        
        struct {
            LunaToken name;
        } variable;
        
        struct {
            LunaToken keyword;
        } this_expr;
        
        struct {
            LunaToken keyword;
            LunaToken method;
        } super_expr;
        
        // Switch case
        struct {
            LunaASTNode* value;
            LunaASTNodeList* statements;
        } case_stmt;
    };
};

// Function prototypes for AST operations
LunaASTNode* luna_create_ast_node(LunaASTNodeType type, int line, int column);
void luna_free_ast_node(LunaASTNode* node);
void luna_print_ast(LunaASTNode* node, int indent);

// AST node list operations
LunaASTNodeList* luna_create_ast_node_list(void);
void luna_ast_node_list_append(LunaASTNodeList* list, LunaASTNode* node);
void luna_free_ast_node_list(LunaASTNodeList* list);

// AST node creation helpers
LunaASTNode* luna_create_program(LunaASTNodeList* statements);
LunaASTNode* luna_create_block(LunaASTNodeList* statements);
LunaASTNode* luna_create_expression_stmt(LunaASTNode* expression);
LunaASTNode* luna_create_var_declaration(LunaToken name, LunaASTNode* initializer, bool is_const);
LunaASTNode* luna_create_func_declaration(LunaToken name, LunaASTNodeList* params, LunaASTNode* body);
LunaASTNode* luna_create_class_declaration(LunaToken name, LunaASTNode* superclass, LunaASTNodeList* methods);
LunaASTNode* luna_create_if_stmt(LunaASTNode* condition, LunaASTNode* then_branch, LunaASTNode* else_branch);
LunaASTNode* luna_create_while_stmt(LunaASTNode* condition, LunaASTNode* body);
LunaASTNode* luna_create_for_stmt(LunaASTNode* initializer, LunaASTNode* condition, LunaASTNode* increment, LunaASTNode* body);
LunaASTNode* luna_create_return_stmt(LunaASTNode* expression);
LunaASTNode* luna_create_break_stmt(void);
LunaASTNode* luna_create_continue_stmt(void);

LunaASTNode* luna_create_binary_expr(LunaASTNode* left, LunaToken operator, LunaASTNode* right);
LunaASTNode* luna_create_unary_expr(LunaToken operator, LunaASTNode* right);
LunaASTNode* luna_create_call_expr(LunaASTNode* callee, LunaASTNodeList* arguments);
LunaASTNode* luna_create_get_expr(LunaASTNode* object, LunaToken name);
LunaASTNode* luna_create_set_expr(LunaASTNode* object, LunaToken name, LunaASTNode* value);
LunaASTNode* luna_create_array_expr(LunaASTNodeList* elements);
LunaASTNode* luna_create_index_expr(LunaASTNode* array, LunaASTNode* index);
LunaASTNode* luna_create_assign_expr(LunaToken name, LunaASTNode* value);
LunaASTNode* luna_create_logical_expr(LunaASTNode* left, LunaToken operator, LunaASTNode* right);
LunaASTNode* luna_create_conditional_expr(LunaASTNode* condition, LunaASTNode* then_expr, LunaASTNode* else_expr);
LunaASTNode* luna_create_literal_expr(LunaValue value);
LunaASTNode* luna_create_variable_expr(LunaToken name);
LunaASTNode* luna_create_this_expr(LunaToken keyword);
LunaASTNode* luna_create_super_expr(LunaToken keyword, LunaToken method);

#endif // LUNA_AST_H
