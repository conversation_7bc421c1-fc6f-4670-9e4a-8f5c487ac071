#include "symbol_table.h"
#include <stdlib.h>
#include <string.h>

#define SYMBOL_TABLE_INITIAL_CAPACITY 16
#define SYMBOL_TABLE_MAX_LOAD 0.75

uint32_t luna_hash_symbol_name(LunaString* name) {
    return name->hash;
}

LunaSymbolTable* luna_create_symbol_table(void) {
    LunaSymbolTable* table = malloc(sizeof(LunaSymbolTable));
    if (table == NULL) {
        fprintf(stderr, "Memory allocation failed for symbol table\n");
        exit(1);
    }
    
    table->capacity = SYMBOL_TABLE_INITIAL_CAPACITY;
    table->count = 0;
    table->buckets = calloc(table->capacity, sizeof(LunaSymbolEntry*));
    
    if (table->buckets == NULL) {
        free(table);
        fprintf(stderr, "Memory allocation failed for symbol table buckets\n");
        exit(1);
    }
    
    return table;
}

void luna_free_symbol_table(LunaSymbolTable* table) {
    if (table == NULL) return;
    
    for (int i = 0; i < table->capacity; i++) {
        LunaSymbolEntry* entry = table->buckets[i];
        while (entry != NULL) {
            LunaSymbolEntry* next = entry->next;
            free(entry);
            entry = next;
        }
    }
    
    free(table->buckets);
    free(table);
}

static void luna_resize_symbol_table(LunaSymbolTable* table) {
    int old_capacity = table->capacity;
    LunaSymbolEntry** old_buckets = table->buckets;
    
    table->capacity *= 2;
    table->count = 0;
    table->buckets = calloc(table->capacity, sizeof(LunaSymbolEntry*));
    
    if (table->buckets == NULL) {
        fprintf(stderr, "Memory allocation failed during symbol table resize\n");
        exit(1);
    }
    
    // Rehash all entries
    for (int i = 0; i < old_capacity; i++) {
        LunaSymbolEntry* entry = old_buckets[i];
        while (entry != NULL) {
            LunaSymbolEntry* next = entry->next;
            
            uint32_t index = luna_hash_symbol_name(entry->symbol.name) % table->capacity;
            entry->next = table->buckets[index];
            table->buckets[index] = entry;
            table->count++;
            
            entry = next;
        }
    }
    
    free(old_buckets);
}

bool luna_symbol_table_set(LunaSymbolTable* table, LunaString* name, LunaSymbol symbol) {
    if (table->count >= table->capacity * SYMBOL_TABLE_MAX_LOAD) {
        luna_resize_symbol_table(table);
    }
    
    uint32_t index = luna_hash_symbol_name(name) % table->capacity;
    LunaSymbolEntry* entry = table->buckets[index];
    
    // Check if symbol already exists
    while (entry != NULL) {
        if (entry->symbol.name == name) {
            entry->symbol = symbol;
            return false; // Updated existing symbol
        }
        entry = entry->next;
    }
    
    // Create new entry
    LunaSymbolEntry* new_entry = malloc(sizeof(LunaSymbolEntry));
    if (new_entry == NULL) {
        fprintf(stderr, "Memory allocation failed for symbol entry\n");
        exit(1);
    }
    
    new_entry->symbol = symbol;
    new_entry->next = table->buckets[index];
    table->buckets[index] = new_entry;
    table->count++;
    
    return true; // Added new symbol
}

bool luna_symbol_table_get(LunaSymbolTable* table, LunaString* name, LunaSymbol* symbol) {
    uint32_t index = luna_hash_symbol_name(name) % table->capacity;
    LunaSymbolEntry* entry = table->buckets[index];
    
    while (entry != NULL) {
        if (entry->symbol.name == name) {
            if (symbol != NULL) {
                *symbol = entry->symbol;
            }
            return true;
        }
        entry = entry->next;
    }
    
    return false;
}

bool luna_symbol_table_delete(LunaSymbolTable* table, LunaString* name) {
    uint32_t index = luna_hash_symbol_name(name) % table->capacity;
    LunaSymbolEntry** entry_ptr = &table->buckets[index];
    
    while (*entry_ptr != NULL) {
        LunaSymbolEntry* entry = *entry_ptr;
        if (entry->symbol.name == name) {
            *entry_ptr = entry->next;
            free(entry);
            table->count--;
            return true;
        }
        entry_ptr = &entry->next;
    }
    
    return false;
}

void luna_symbol_table_clear(LunaSymbolTable* table) {
    for (int i = 0; i < table->capacity; i++) {
        LunaSymbolEntry* entry = table->buckets[i];
        while (entry != NULL) {
            LunaSymbolEntry* next = entry->next;
            free(entry);
            entry = next;
        }
        table->buckets[i] = NULL;
    }
    table->count = 0;
}

// Scope operations
LunaScope* luna_create_scope(LunaScope* enclosing, int depth) {
    LunaScope* scope = malloc(sizeof(LunaScope));
    if (scope == NULL) {
        fprintf(stderr, "Memory allocation failed for scope\n");
        exit(1);
    }
    
    scope->symbols = luna_create_symbol_table();
    scope->depth = depth;
    scope->local_count = 0;
    scope->enclosing = enclosing;
    scope->is_function_scope = false;
    scope->is_class_scope = false;
    scope->is_loop_scope = false;
    
    return scope;
}

void luna_free_scope(LunaScope* scope) {
    if (scope == NULL) return;
    
    luna_free_symbol_table(scope->symbols);
    free(scope);
}

bool luna_scope_define(LunaScope* scope, LunaString* name, LunaSymbol symbol) {
    symbol.depth = scope->depth;
    if (symbol.type == SYMBOL_LOCAL || symbol.type == SYMBOL_PARAMETER) {
        symbol.index = scope->local_count++;
    }
    
    return luna_symbol_table_set(scope->symbols, name, symbol);
}

bool luna_scope_get(LunaScope* scope, LunaString* name, LunaSymbol* symbol) {
    return luna_symbol_table_get(scope->symbols, name, symbol);
}

bool luna_scope_get_local(LunaScope* scope, LunaString* name, LunaSymbol* symbol) {
    // Only search in the current scope, not enclosing scopes
    return luna_scope_get(scope, name, symbol);
}

bool luna_scope_set(LunaScope* scope, LunaString* name, LunaValue value) {
    LunaSymbol symbol;
    if (luna_scope_get(scope, name, &symbol)) {
        symbol.value = value;
        symbol.is_initialized = true;
        return luna_symbol_table_set(scope->symbols, name, symbol);
    }
    return false;
}

// Scope manager operations
void luna_init_scope_manager(LunaScopeManager* manager) {
    manager->global = luna_create_scope(NULL, 0);
    manager->current = manager->global;
    manager->max_depth = 0;
}

void luna_free_scope_manager(LunaScopeManager* manager) {
    LunaScope* scope = manager->current;
    while (scope != NULL) {
        LunaScope* enclosing = scope->enclosing;
        luna_free_scope(scope);
        scope = enclosing;
    }
}

void luna_push_scope(LunaScopeManager* manager, bool is_function, bool is_class, bool is_loop) {
    int depth = manager->current->depth + 1;
    LunaScope* new_scope = luna_create_scope(manager->current, depth);
    
    new_scope->is_function_scope = is_function;
    new_scope->is_class_scope = is_class;
    new_scope->is_loop_scope = is_loop;
    
    manager->current = new_scope;
    if (depth > manager->max_depth) {
        manager->max_depth = depth;
    }
}

void luna_pop_scope(LunaScopeManager* manager) {
    if (manager->current != manager->global) {
        LunaScope* old_scope = manager->current;
        manager->current = manager->current->enclosing;
        luna_free_scope(old_scope);
    }
}

bool luna_define_variable(LunaScopeManager* manager, LunaString* name, LunaValue value, bool is_const) {
    LunaSymbol symbol;
    symbol.name = name;
    symbol.type = (manager->current->depth == 0) ? SYMBOL_GLOBAL : SYMBOL_LOCAL;
    symbol.value = value;
    symbol.depth = manager->current->depth;
    symbol.index = -1;
    symbol.is_captured = false;
    symbol.is_const = is_const;
    symbol.is_initialized = !LUNA_IS_NIL(value);
    
    return luna_scope_define(manager->current, name, symbol);
}

bool luna_get_variable(LunaScopeManager* manager, LunaString* name, LunaSymbol* symbol) {
    LunaScope* scope = manager->current;
    
    while (scope != NULL) {
        if (luna_scope_get(scope, name, symbol)) {
            return true;
        }
        scope = scope->enclosing;
    }
    
    return false;
}

bool luna_set_variable(LunaScopeManager* manager, LunaString* name, LunaValue value) {
    LunaScope* scope = manager->current;
    
    while (scope != NULL) {
        LunaSymbol symbol;
        if (luna_scope_get(scope, name, &symbol)) {
            if (symbol.is_const) {
                return false; // Cannot modify const variable
            }
            return luna_scope_set(scope, name, value);
        }
        scope = scope->enclosing;
    }
    
    return false;
}

bool luna_is_variable_defined(LunaScopeManager* manager, LunaString* name) {
    LunaSymbol symbol;
    return luna_get_variable(manager, name, &symbol);
}

// Function and class management
bool luna_define_function(LunaScopeManager* manager, LunaString* name, LunaFunction* function) {
    LunaSymbol symbol;
    symbol.name = name;
    symbol.type = SYMBOL_FUNCTION;
    symbol.value = LUNA_OBJECT_VAL(function);
    symbol.depth = manager->current->depth;
    symbol.index = -1;
    symbol.is_captured = false;
    symbol.is_const = true; // Functions are immutable
    symbol.is_initialized = true;

    return luna_scope_define(manager->current, name, symbol);
}

bool luna_define_class(LunaScopeManager* manager, LunaString* name, LunaClass* klass) {
    LunaSymbol symbol;
    symbol.name = name;
    symbol.type = SYMBOL_CLASS;
    symbol.value = LUNA_OBJECT_VAL(klass);
    symbol.depth = manager->current->depth;
    symbol.index = -1;
    symbol.is_captured = false;
    symbol.is_const = true; // Classes are immutable
    symbol.is_initialized = true;

    return luna_scope_define(manager->current, name, symbol);
}

bool luna_define_parameter(LunaScopeManager* manager, LunaString* name, int index) {
    LunaSymbol symbol;
    symbol.name = name;
    symbol.type = SYMBOL_PARAMETER;
    symbol.value = LUNA_NIL_VAL;
    symbol.depth = manager->current->depth;
    symbol.index = index;
    symbol.is_captured = false;
    symbol.is_const = false;
    symbol.is_initialized = true; // Parameters are initialized by the caller

    return luna_scope_define(manager->current, name, symbol);
}

// Upvalue resolution
int luna_resolve_local(LunaScope* scope, LunaString* name) {
    LunaSymbol symbol;
    if (luna_scope_get_local(scope, name, &symbol)) {
        if (symbol.type == SYMBOL_LOCAL || symbol.type == SYMBOL_PARAMETER) {
            return symbol.index;
        }
    }
    return -1;
}

int luna_resolve_upvalue(LunaScopeManager* manager, LunaString* name) {
    if (manager->current->enclosing == NULL) {
        return -1; // No enclosing scope
    }

    // Try to resolve in the immediately enclosing scope
    int local = luna_resolve_local(manager->current->enclosing, name);
    if (local != -1) {
        // Mark the variable as captured
        LunaSymbol symbol;
        if (luna_scope_get(manager->current->enclosing, name, &symbol)) {
            symbol.is_captured = true;
            luna_symbol_table_set(manager->current->enclosing->symbols, name, symbol);
        }
        return local;
    }

    // Recursively resolve in outer scopes
    LunaScope* saved_current = manager->current;
    manager->current = manager->current->enclosing;
    int upvalue = luna_resolve_upvalue(manager, name);
    manager->current = saved_current;

    return upvalue;
}

// Scope queries
bool luna_is_in_function_scope(LunaScopeManager* manager) {
    LunaScope* scope = manager->current;
    while (scope != NULL) {
        if (scope->is_function_scope) {
            return true;
        }
        scope = scope->enclosing;
    }
    return false;
}

bool luna_is_in_class_scope(LunaScopeManager* manager) {
    LunaScope* scope = manager->current;
    while (scope != NULL) {
        if (scope->is_class_scope) {
            return true;
        }
        scope = scope->enclosing;
    }
    return false;
}

bool luna_is_in_loop_scope(LunaScopeManager* manager) {
    LunaScope* scope = manager->current;
    while (scope != NULL) {
        if (scope->is_loop_scope) {
            return true;
        }
        scope = scope->enclosing;
    }
    return false;
}

int luna_get_current_depth(LunaScopeManager* manager) {
    return manager->current->depth;
}

// Error checking
bool luna_check_variable_redefinition(LunaScopeManager* manager, LunaString* name) {
    LunaSymbol symbol;
    return luna_scope_get_local(manager->current, name, &symbol);
}

bool luna_check_const_assignment(LunaScopeManager* manager, LunaString* name) {
    LunaSymbol symbol;
    if (luna_get_variable(manager, name, &symbol)) {
        return symbol.is_const;
    }
    return false;
}

bool luna_check_uninitialized_variable(LunaScopeManager* manager, LunaString* name) {
    LunaSymbol symbol;
    if (luna_get_variable(manager, name, &symbol)) {
        return !symbol.is_initialized;
    }
    return true; // Variable not found, so it's uninitialized
}

// Utility functions
void luna_print_symbol_table(LunaSymbolTable* table) {
    printf("Symbol Table (count: %d, capacity: %d):\n", table->count, table->capacity);
    for (int i = 0; i < table->capacity; i++) {
        LunaSymbolEntry* entry = table->buckets[i];
        while (entry != NULL) {
            printf("  %s: type=%d, depth=%d, index=%d\n",
                   entry->symbol.name->chars,
                   entry->symbol.type,
                   entry->symbol.depth,
                   entry->symbol.index);
            entry = entry->next;
        }
    }
}

void luna_print_scope(LunaScope* scope) {
    printf("Scope (depth: %d, locals: %d):\n", scope->depth, scope->local_count);
    luna_print_symbol_table(scope->symbols);
}
