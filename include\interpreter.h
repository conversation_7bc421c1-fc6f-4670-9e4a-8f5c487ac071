#ifndef LUNA_INTERPRETER_H
#define LUNA_INTERPRETER_H

#include "luna.h"
#include "vm.h"
#include "ast.h"
#include "symbol_table.h"

// Interpreter state
typedef struct {
    LunaVM* vm;
    LunaScopeManager* scope_manager;
    bool has_error;
    LunaError error;
    
    // Control flow state
    bool is_returning;
    bool is_breaking;
    bool is_continuing;
    LunaValue return_value;
    
    // Exception handling
    bool is_throwing;
    LunaValue exception_value;
} LunaInterpreter;

// Interpreter functions
void luna_init_interpreter(LunaInterpreter* interpreter, LunaVM* vm);
void luna_free_interpreter(LunaInterpreter* interpreter);

// Main evaluation functions
LunaValue luna_evaluate(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_expression(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_statement(LunaInterpreter* interpreter, LunaASTNode* node);

// Expression evaluation
LunaValue luna_evaluate_literal(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_variable(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_binary(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_unary(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_call(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_get(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_set(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_array(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_index(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_assign(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_logical(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_conditional(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_this(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_evaluate_super(LunaInterpreter* interpreter, LunaASTNode* node);

// Statement execution
void luna_execute_program(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_block(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_expression_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_var_declaration(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_func_declaration(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_class_declaration(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_if_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_while_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_for_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_switch_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_return_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_break_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_continue_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_try_stmt(LunaInterpreter* interpreter, LunaASTNode* node);
void luna_execute_throw_stmt(LunaInterpreter* interpreter, LunaASTNode* node);

// Utility functions
bool luna_is_truthy(LunaValue value);
bool luna_is_equal(LunaValue a, LunaValue b);
LunaValue luna_perform_binary_operation(LunaInterpreter* interpreter, LunaValue left, LunaToken operator, LunaValue right);
LunaValue luna_perform_unary_operation(LunaInterpreter* interpreter, LunaToken operator, LunaValue operand);

// Function and class handling
LunaFunction* luna_create_function_from_declaration(LunaInterpreter* interpreter, LunaASTNode* node);
LunaClass* luna_create_class_from_declaration(LunaInterpreter* interpreter, LunaASTNode* node);
LunaValue luna_call_function(LunaInterpreter* interpreter, LunaFunction* function, LunaASTNodeList* arguments);
LunaValue luna_call_native_function(LunaInterpreter* interpreter, LunaNativeFunction* function, LunaASTNodeList* arguments);

// Error handling
void luna_interpreter_error(LunaInterpreter* interpreter, const char* format, ...);
void luna_interpreter_runtime_error(LunaInterpreter* interpreter, LunaASTNode* node, const char* format, ...);

// Function call helper
LunaValue luna_call_function(LunaInterpreter* interpreter, LunaFunction* function, int arg_count, LunaValue* args);

// Control flow helpers
void luna_set_return(LunaInterpreter* interpreter, LunaValue value);
void luna_set_break(LunaInterpreter* interpreter);
void luna_set_continue(LunaInterpreter* interpreter);
void luna_clear_control_flow(LunaInterpreter* interpreter);
bool luna_should_stop_execution(LunaInterpreter* interpreter);

// Exception handling
void luna_throw_exception(LunaInterpreter* interpreter, LunaValue exception);
void luna_clear_exception(LunaInterpreter* interpreter);

#endif // LUNA_INTERPRETER_H
